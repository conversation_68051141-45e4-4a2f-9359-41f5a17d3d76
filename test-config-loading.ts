/**
 * 测试配置加载功能
 * v1.4.0 - Sub-Agents 革命
 */

import { loadSubAgentsConfig } from "./src/utils.js";

/**
 * 测试配置加载功能
 */
async function testConfigLoading() {
  console.log('🧪 Testing Configuration Loading Functionality...\n');

  try {
    console.log('📖 Loading Sub-Agents configuration from .vibecode/workflows/config.json...');
    
    const config = await loadSubAgentsConfig('.');
    
    console.log('✅ Configuration loaded successfully!\n');
    
    console.log('⚙️ Sub-Agents Configuration Summary:');
    console.log('=' .repeat(60));
    
    // 质量门控配置
    console.log(`📊 Quality Gates Configuration:`);
    console.log(`   Overall Threshold: ${config.qualityGates.overallThreshold}%`);
    console.log(`   Default Threshold: ${config.qualityGates.defaultThreshold}%`);
    console.log(`   Strict Mode: ${config.qualityGates.strictMode ? 'Enabled' : 'Disabled'}`);
    console.log(`   Phase-specific Thresholds:`);
    Object.entries(config.qualityGates.phaseThresholds).forEach(([phase, threshold]) => {
      console.log(`     • ${phase}: ${threshold}%`);
    });
    console.log('');
    
    // 代理配置
    console.log(`🤖 Agents Configuration:`);
    Object.entries(config.agents).forEach(([agentName, agentConfig]) => {
      const status = agentConfig.enabled ? '🟢 Enabled' : '🔴 Disabled';
      console.log(`   • ${agentName}: ${status}`);
      console.log(`     Timeout: ${agentConfig.timeout}ms`);
      
      // 显示特定代理的额外配置
      if (agentName === 'orchestrator') {
        console.log(`     Max Retries: ${agentConfig.maxRetries}`);
        console.log(`     Max Workflow Retries: ${agentConfig.maxWorkflowRetries}`);
      } else if (agentName === 'spec') {
        console.log(`     Output Format: ${agentConfig.outputFormat}`);
      } else if (agentName === 'architect') {
        console.log(`     Preferred Stack: ${agentConfig.preferredStack}`);
      } else if (agentName === 'developer') {
        console.log(`     Code Quality: ${agentConfig.codeQuality}`);
        console.log(`     Improvement Bonus: ${agentConfig.improvementBonus}`);
      } else if (agentName === 'quality') {
        console.log(`     Security Scan: ${agentConfig.securityScan ? 'Yes' : 'No'}`);
        console.log(`     Performance Check: ${agentConfig.performanceCheck ? 'Yes' : 'No'}`);
      } else if (agentName === 'test') {
        console.log(`     Coverage Target: ${agentConfig.coverageTarget}%`);
        console.log(`     Test Types: ${agentConfig.testTypes.join(', ')}`);
      }
      console.log('');
    });
    
    // 工作流程配置
    console.log(`🔄 Workflow Configuration:`);
    console.log(`   Auto Restart: ${config.workflow.autoRestart ? 'Enabled' : 'Disabled'}`);
    console.log(`   Continue on Phase Failure: ${config.workflow.continueOnPhaseFailure ? 'Yes' : 'No'}`);
    console.log(`   Progress Reporting: ${config.workflow.progressReporting ? 'Enabled' : 'Disabled'}`);
    console.log(`   Workflow Phases:`);
    config.workflow.phases.forEach((phase, index) => {
      console.log(`     ${index + 1}. ${phase}`);
    });
    console.log('');
    
    // 输出配置
    console.log(`📤 Output Configuration:`);
    console.log(`   Format: ${config.output.format}`);
    console.log(`   Show Progress: ${config.output.showProgress ? 'Yes' : 'No'}`);
    console.log(`   Show Quality Scores: ${config.output.showQualityScores ? 'Yes' : 'No'}`);
    console.log(`   Save Results: ${config.output.saveResults ? 'Yes' : 'No'}`);
    if (config.output.saveResults) {
      console.log(`   Result Path: ${config.output.resultPath}`);
    }
    console.log('');
    
    // 通信配置
    console.log(`📡 Communication Configuration:`);
    console.log(`   Message Bus:`);
    console.log(`     Timeout: ${config.communication.messageBus.timeout}ms`);
    console.log(`     Retry Attempts: ${config.communication.messageBus.retryAttempts}`);
    console.log(`     Buffer Size: ${config.communication.messageBus.bufferSize}`);
    console.log(`   Logging:`);
    console.log(`     Level: ${config.communication.logging.level}`);
    console.log(`     Save to File: ${config.communication.logging.saveToFile ? 'Yes' : 'No'}`);
    if (config.communication.logging.saveToFile) {
      console.log(`     Log Path: ${config.communication.logging.logPath}`);
    }
    console.log('');
    
    // 验证配置的有效性
    console.log('🔍 Configuration Validation:');
    console.log('=' .repeat(60));
    
    const validationResults = validateConfiguration(config);
    
    if (validationResults.isValid) {
      console.log('✅ Configuration is valid and ready to use!');
    } else {
      console.log('❌ Configuration has issues:');
      validationResults.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
    }
    
    console.log('\n🎉 Configuration loading test completed successfully!');
    
    // 显示如何使用配置
    console.log('\n💡 Usage Examples:');
    console.log('=' .repeat(60));
    console.log('1. Use default configuration:');
    console.log('   vibe-coding "开发用户认证系统" --rootPath ./project');
    console.log('');
    console.log('2. Override quality threshold:');
    console.log('   vibe-coding "开发用户认证系统" --rootPath ./project --qualityThreshold 98');
    console.log('');
    console.log('3. Show current configuration:');
    console.log('   show-config --rootPath ./project');
    console.log('');
    console.log('4. Edit configuration file:');
    console.log('   Edit .vibecode/workflows/config.json to customize behavior');

  } catch (error) {
    console.error('❌ Configuration loading test failed:', error);
    process.exit(1);
  }
}

/**
 * 验证配置的有效性
 */
function validateConfiguration(config: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 验证质量阈值
  if (config.qualityGates.overallThreshold < 0 || config.qualityGates.overallThreshold > 100) {
    errors.push('Overall quality threshold must be between 0 and 100');
  }
  
  // 验证阶段阈值
  Object.entries(config.qualityGates.phaseThresholds).forEach(([phase, threshold]) => {
    if (typeof threshold !== 'number' || threshold < 0 || threshold > 100) {
      errors.push(`Phase threshold for ${phase} must be between 0 and 100`);
    }
  });
  
  // 验证代理配置
  Object.entries(config.agents).forEach(([agentName, agentConfig]: [string, any]) => {
    if (typeof agentConfig.timeout !== 'number' || agentConfig.timeout <= 0) {
      errors.push(`Timeout for ${agentName} agent must be a positive number`);
    }
  });
  
  // 验证工作流程阶段
  if (!Array.isArray(config.workflow.phases) || config.workflow.phases.length === 0) {
    errors.push('Workflow phases must be a non-empty array');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testConfigLoading().catch(console.error);
}

export { testConfigLoading };
