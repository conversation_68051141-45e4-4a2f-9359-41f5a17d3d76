{"version": "1.3.0", "lastUpdated": "2025-07-30T10:30:00Z", "workflows": {"specs": {"enabled": true, "defaultStatus": "created", "statusFlow": ["created", "requirements", "design", "tasks", "implementing", "completed"], "templates": {"overview": ".vibecode/templates/spec-templates/overview.md", "requirements": ".vibecode/templates/spec-templates/requirements.md", "design": ".vibecode/templates/spec-templates/design.md", "tasks": ".vibecode/templates/spec-templates/tasks.md"}, "autoGenerate": {"requirements": true, "design": true, "tasks": true}}, "bugs": {"enabled": true, "defaultStatus": "reported", "statusFlow": ["reported", "analyzing", "fixing", "verifying", "resolved"], "severityLevels": ["low", "medium", "high", "critical"], "templates": {"report": ".vibecode/templates/bug-templates/report.md", "analysis": ".vibecode/templates/bug-templates/analysis.md", "fix": ".vibecode/templates/bug-templates/fix.md", "verification": ".vibecode/templates/bug-templates/verification.md"}, "autoGenerate": {"analysis": true, "fix": true, "verification": true}}}, "analysis": {"enabled": true, "modules": ["codebase-analysis", "dependency-mapping", "architecture-assessment", "evolution-tracking"], "outputFormats": ["markdown", "json", "mermaid"]}, "prediction": {"enabled": true, "modules": ["issue-prediction", "performance-assessment", "security-scanning", "debt-measurement"], "timeFrames": ["immediate", "short-term", "medium-term", "long-term"], "riskLevels": ["low", "medium", "high", "critical"]}, "generation": {"enabled": true, "modules": ["code-generation", "refactor-suggestions", "test-generation", "performance-optimization"], "supportedLanguages": ["typescript", "javascript", "python", "java", "go", "rust"], "qualityChecks": ["readability", "maintainability", "performance", "security", "testability"]}, "steering": {"enabled": true, "documents": ["product.md", "tech.md", "structure.md"], "autoUpdate": true, "integrationLevel": "full"}, "notifications": {"enabled": true, "channels": ["console", "file"], "levels": ["info", "warning", "error"]}, "performance": {"maxResponseTime": 2000, "maxMemoryUsage": 536870912, "concurrentTasks": 5, "cacheEnabled": true}, "security": {"sandboxMode": true, "allowedPaths": [".vibecode/", "src/", "package.json", "tsconfig.json"], "blockedOperations": ["exec", "spawn", "network"]}}