# 技术栈 - Vibe Coding

## 🛠️ 开发语言和框架

### 核心技术栈
- **TypeScript (v5.7.3)** - 主要开发语言，提供类型安全和现代 JavaScript 特性
- **Node.js** - 运行时环境，支持服务器端 JavaScript 执行
- **@modelcontextprotocol/sdk (v1.5.0)** - MCP 协议实现，用于 AI 模型上下文协议

### 依赖管理
- **pnpm** - 包管理器，提供高效的依赖管理和磁盘空间优化
- **Zod (v3.24.2)** - 运行时类型验证和解析库

### 开发工具链
- **ESLint/TSLint** - 代码质量检查和风格统一
- **TypeScript Compiler API** - 用于代码分析和 AST 解析
- **fs 模块** - 文件系统操作，用于项目文件读写

## 🔧 开发工具和实践

### 代码分析工具
- **TypeScript AST Parser** - 语法树解析和代码结构分析
- **依赖关系分析器** - 模块依赖图谱生成和循环依赖检测
- **架构模式识别器** - 设计模式和 SOLID 原则检查

### 质量保证工具
- **代码质量评估器** - 多维度质量评分系统
- **安全漏洞扫描器** - 7 类安全问题检测
- **性能分析器** - 瓶颈识别和优化建议

### 预测分析工具
- **问题预测引擎** - 8 种问题类型预测
- **技术债务量化器** - 5 类债务分析和优先级排序
- **风险评估系统** - 4 级风险等级评估

### 代码生成工具
- **智能代码生成器** - 上下文感知的代码生成
- **重构建议引擎** - 基于预测分析的重构优化
- **测试生成器** - 自动化测试用例生成
- **性能优化器** - 算法和架构优化建议

## 📋 技术约束和要求

### 性能要求
- **响应时间** - 工具调用响应时间 < 2 秒
- **内存使用** - 峰值内存使用 < 512MB
- **并发处理** - 支持同时处理多个分析任务

### 兼容性要求
- **Node.js 版本** - 支持 Node.js 16+ 
- **TypeScript 版本** - 支持 TypeScript 4.5+
- **操作系统** - 支持 Windows、macOS、Linux

### 安全要求
- **代码安全** - 不执行用户提供的任意代码
- **文件访问** - 仅访问项目目录内的文件
- **数据隐私** - 不上传或泄露项目代码

### 可扩展性要求
- **模块化设计** - 支持插件式功能扩展
- **配置化** - 支持自定义分析规则和模板
- **API 兼容** - 保持向后兼容的 API 接口

## 🔗 第三方集成

### MCP 协议集成
- **@modelcontextprotocol/sdk** - 核心 MCP 协议实现
- **StdioServerTransport** - 标准输入输出传输层
- **工具注册系统** - 动态工具注册和管理

### 文件系统集成
- **fs/promises** - 异步文件操作
- **path 模块** - 跨平台路径处理
- **glob 模式** - 文件匹配和过滤

### 代码分析集成
- **TypeScript Compiler API** - 官方编译器 API
- **AST 遍历器** - 语法树遍历和分析
- **类型检查器** - TypeScript 类型系统集成

## 🏗️ 架构模式

### 模块化架构
```
src/
├── analysis/     # 代码分析模块
├── prediction/   # 预测分析模块  
├── generation/   # 代码生成模块
├── tools/        # 工具实现模块
├── types.ts      # 类型定义
├── utils.ts      # 工具函数
└── server.ts     # 服务器配置
```

### 设计模式
- **工厂模式** - 工具实例创建和管理
- **策略模式** - 不同分析策略的实现
- **观察者模式** - 工作流程状态变更通知
- **模板方法模式** - 工作流程模板化执行

### 数据流架构
1. **输入层** - MCP 协议请求接收
2. **处理层** - 业务逻辑和分析处理
3. **存储层** - 文件系统和配置管理
4. **输出层** - 结果格式化和返回

## 🔄 开发流程

### 代码组织原则
- **单一职责** - 每个模块专注单一功能
- **依赖注入** - 通过参数传递依赖关系
- **接口隔离** - 定义清晰的模块接口
- **开闭原则** - 对扩展开放，对修改封闭

### 命名约定
- **文件命名** - kebab-case（如 `code-analysis.ts`）
- **变量命名** - camelCase（如 `analysisResult`）
- **类命名** - PascalCase（如 `CodeAnalyzer`）
- **常量命名** - UPPER_SNAKE_CASE（如 `MAX_FILE_SIZE`）

### 错误处理
- **统一错误格式** - 标准化错误响应结构
- **错误分类** - 按严重程度和类型分类
- **日志记录** - 详细的错误日志和调试信息
- **优雅降级** - 部分功能失败时的备选方案

---

*最后更新：2025-07-30*
*版本：v1.3.0*
