# 项目结构 - Vibe Coding

## 📁 文件组织模式

### 根目录结构
```
vibe-coding/
├── .codelf/              # 项目文档和配置目录
│   ├── steering/         # Steering 文档系统
│   ├── workflows/        # 工作流程管理
│   ├── templates/        # 模板文件
│   └── *.md             # 项目文档
├── src/                  # 源代码目录
│   ├── analysis/         # 代码分析模块
│   ├── prediction/       # 预测分析模块
│   ├── generation/       # 代码生成模块
│   ├── tools/           # 工具实现模块
│   └── *.ts             # 核心文件
├── node_modules/         # 依赖包目录
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
└── README*.md           # 项目说明文档
```

### .codelf 目录详细结构
```
.codelf/
├── steering/                    # Steering 文档系统
│   ├── product.md              # 产品概述和愿景
│   ├── tech.md                 # 技术栈和工具链
│   └── structure.md            # 项目结构规范
├── workflows/                   # 工作流程管理
│   ├── specs/                  # 规范工作流
│   │   └── {spec-name}/        # 具体规范目录
│   │       ├── overview.md     # 规范概述
│   │       ├── requirements.md # 需求文档
│   │       ├── design.md       # 设计文档
│   │       └── tasks.md        # 任务分解
│   ├── bugs/                   # Bug 修复工作流
│   │   └── {bug-name}/         # 具体 Bug 目录
│   │       ├── report.md       # Bug 报告
│   │       ├── analysis.md     # 根因分析
│   │       ├── fix.md          # 修复方案
│   │       └── verification.md # 验证测试
│   └── config.json             # 工作流程配置
├── templates/                   # 模板文件目录
│   ├── spec-templates/         # 规范模板
│   ├── bug-templates/          # Bug 模板
│   └── code-templates/         # 代码模板
├── project.md                  # 项目结构文档
├── changelog.md                # 变更记录
└── attention.md                # 开发注意事项
```

### src 目录详细结构
```
src/
├── analysis/                   # 代码分析模块
│   ├── types.ts               # 分析类型定义
│   ├── engine.ts              # 分析引擎核心
│   ├── parser.ts              # AST 解析器
│   ├── dependencies.ts        # 依赖关系分析
│   ├── architecture.ts        # 架构模式分析
│   └── quality.ts             # 质量指标计算
├── prediction/                 # 预测分析模块
│   ├── types.ts               # 预测类型定义
│   ├── engine.ts              # 预测引擎核心
│   ├── issues.ts              # 问题预测分析
│   ├── performance.ts         # 性能风险评估
│   ├── security.ts            # 安全漏洞扫描
│   └── debt.ts                # 技术债务量化
├── generation/                 # 代码生成模块
│   ├── types.ts               # 生成类型定义
│   ├── engine.ts              # 生成引擎核心
│   ├── context.ts             # 上下文分析器
│   ├── templates.ts           # 代码模板系统
│   ├── quality.ts             # 代码质量检查
│   ├── nlp.ts                 # NLP 处理模块
│   └── refactor.ts            # 重构建议引擎
├── tools/                      # 工具实现模块
│   ├── basic.ts               # 基础工具
│   ├── steering.ts            # Steering 工具
│   ├── spec.ts                # 规范工具
│   ├── bug.ts                 # Bug 工具
│   ├── intelligence.ts        # 智能分析工具
│   ├── prediction.ts          # 预测分析工具
│   └── generation.ts          # 代码生成工具
├── types.ts                    # 全局类型定义
├── utils.ts                    # 工具函数
├── server.ts                   # MCP 服务器配置
└── index.ts                    # 主入口文件
```

## 🏷️ 命名约定

### 文件命名规范
- **TypeScript 文件** - kebab-case（如 `code-analysis.ts`）
- **目录名称** - kebab-case（如 `analysis/`, `prediction/`）
- **配置文件** - 标准名称（如 `package.json`, `tsconfig.json`）
- **文档文件** - kebab-case + .md（如 `workflow-readme.md`）

### 变量命名规范
- **变量和函数** - camelCase（如 `analysisResult`, `generateCode`）
- **类和接口** - PascalCase（如 `CodeAnalyzer`, `AnalysisResult`）
- **常量** - UPPER_SNAKE_CASE（如 `MAX_FILE_SIZE`, `DEFAULT_TIMEOUT`）
- **枚举** - PascalCase（如 `AnalysisType`, `WorkflowStatus`）

### 类型命名规范
- **接口** - PascalCase + Interface 后缀（如 `AnalysisResultInterface`）
- **类型别名** - PascalCase + Type 后缀（如 `ConfigurationType`）
- **泛型参数** - 单个大写字母（如 `T`, `K`, `V`）

## 📦 导入模式

### 相对导入
```typescript
// 同级目录
import { utils } from './utils.js';

// 上级目录
import { types } from '../types.js';

// 子目录
import { engine } from './analysis/engine.js';
```

### 绝对导入
```typescript
// 第三方库
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';

// Node.js 内置模块
import { promises as fs } from 'fs';
import path from 'path';
```

### 导入顺序
1. **Node.js 内置模块** - fs, path, util 等
2. **第三方库** - npm 包
3. **项目内部模块** - 相对路径导入
4. **类型导入** - 使用 `import type` 语法

## 🔧 代码组织原则

### 模块职责分离
- **analysis/** - 专注代码分析和理解
- **prediction/** - 专注预测分析和风险评估
- **generation/** - 专注代码生成和优化
- **tools/** - 专注工具实现和 MCP 集成

### 依赖关系管理
- **单向依赖** - 避免循环依赖
- **接口隔离** - 通过接口定义模块边界
- **依赖注入** - 通过参数传递依赖

### 配置管理
- **集中配置** - 统一的配置文件管理
- **环境分离** - 开发、测试、生产环境配置
- **类型安全** - 使用 Zod 进行配置验证

## 📝 文档组织

### 文档层次结构
1. **README.md** - 项目概述和快速开始
2. **WORKFLOW_README.md** - 工作流程详细说明
3. **.codelf/steering/** - 项目核心文档
4. **.codelf/workflows/** - 工作流程文档
5. **代码注释** - 函数和类的详细说明

### 文档更新策略
- **自动生成** - 通过工具自动更新项目结构文档
- **版本控制** - 文档变更与代码变更同步
- **定期审查** - 定期检查文档的准确性和完整性

## 🔄 工作流程集成

### Git 工作流程
- **分支策略** - feature/bugfix/hotfix 分支模式
- **提交规范** - 使用 Conventional Commits 格式
- **代码审查** - 强制 Pull Request 审查流程

### CI/CD 集成
- **自动测试** - 代码提交时自动运行测试
- **质量检查** - ESLint、TypeScript 编译检查
- **文档更新** - 自动更新项目文档

---

*最后更新：2025-07-30*
*版本：v1.3.0*
