/**
 * Utility functions for Vibe Coding workflow system
 */

import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { WorkflowConfig, SubAgentsConfig } from "./types.js";

/**
 * Ensure workflow directories exist
 */
export async function ensureWorkflowDirectories(rootPath: string): Promise<void> {
  const workflowDirs = [
    path.join(rootPath, ".vibecode", "steering"),
    path.join(rootPath, ".vibecode", "workflows", "specs"),
    path.join(rootPath, ".vibecode", "workflows", "bugs"),
    path.join(rootPath, ".vibecode", "templates")
  ];

  for (const dir of workflowDirs) {
    await fs.mkdir(dir, { recursive: true });
  }
}

/**
 * Load workflow configuration
 */
export async function loadWorkflowConfig(rootPath: string): Promise<WorkflowConfig> {
  const configPath = path.join(rootPath, ".vibecode", "workflows", "config.json");

  try {
    const content = await fs.readFile(configPath, "utf-8");
    return JSON.parse(content);
  } catch {
    // If config file doesn't exist, return default config
    const defaultConfig: WorkflowConfig = {
      specs: {},
      bugs: {},
      version: "1.0.0"
    };
    await saveWorkflowConfig(rootPath, defaultConfig);
    return defaultConfig;
  }
}

/**
 * Save workflow configuration
 */
export async function saveWorkflowConfig(rootPath: string, config: WorkflowConfig): Promise<void> {
  const configPath = path.join(rootPath, ".vibecode", "workflows", "config.json");
  await fs.writeFile(configPath, JSON.stringify(config, null, 2), "utf-8");
}

/**
 * Generate spec name from title
 */
export function generateSpecName(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * Get current timestamp
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Load Sub-Agents configuration from .vibecode/workflows/config.json
 */
export async function loadSubAgentsConfig(rootPath: string): Promise<SubAgentsConfig> {
  const config = await loadWorkflowConfig(rootPath);

  if (config.subAgents) {
    return config.subAgents;
  }

  // Return default Sub-Agents configuration if not found
  const defaultSubAgentsConfig: SubAgentsConfig = {
    enabled: true,
    version: "1.4.0",
    qualityGates: {
      defaultThreshold: 95,
      phaseThresholds: {
        requirements: 95,
        architecture: 95,
        implementation: 85,
        quality: 75,
        testing: 70,
        integration: 75
      },
      overallThreshold: 95,
      strictMode: false
    },
    agents: {
      orchestrator: {
        enabled: true,
        maxRetries: 3,
        maxWorkflowRetries: 2,
        timeout: 30000
      },
      spec: {
        enabled: true,
        timeout: 15000,
        outputFormat: "EARS"
      },
      architect: {
        enabled: true,
        timeout: 20000,
        preferredStack: "typescript"
      },
      developer: {
        enabled: true,
        timeout: 25000,
        codeQuality: "A",
        improvementBonus: 3
      },
      quality: {
        enabled: true,
        timeout: 15000,
        securityScan: true,
        performanceCheck: true
      },
      test: {
        enabled: true,
        timeout: 20000,
        coverageTarget: 90,
        testTypes: ["unit", "integration"]
      }
    },
    workflow: {
      phases: [
        "initialization",
        "requirements",
        "architecture",
        "implementation",
        "quality",
        "testing",
        "integration",
        "completion"
      ],
      autoRestart: true,
      continueOnPhaseFailure: true,
      progressReporting: true
    },
    output: {
      format: "detailed",
      showProgress: true,
      showQualityScores: true,
      saveResults: true,
      resultPath: ".vibecode/workflows/results"
    },
    communication: {
      messageBus: {
        timeout: 5000,
        retryAttempts: 3,
        bufferSize: 1000
      },
      logging: {
        level: "info",
        saveToFile: true,
        logPath: ".vibecode/workflows/logs"
      }
    }
  };

  return defaultSubAgentsConfig;
}

/**
 * Parse .gitignore file
 */
export async function parseGitignore(
  rootPath: string,
  targetPath: string
): Promise<boolean | null> {
  const gitignorePath = path.join(rootPath, ".gitignore");

  // Check if .gitignore file exists
  if (!existsSync(gitignorePath)) {
    return null;
  }

  try {
    // Read .gitignore file content
    const content = await fs.readFile(gitignorePath, "utf-8");
    // Use gitignore-parser's compile method to parse .gitignore content
    const gitignoreParser = await import("gitignore-parser");
    const gitignore = gitignoreParser.compile(content);

    // Use denies method to check if path is rejected (ignored)
    return gitignore.denies(targetPath);
  } catch (error) {
    console.error("Error parsing .gitignore:", error);
    return null;
  }
}
