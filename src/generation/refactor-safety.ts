/**
 * 智能代码生成 - 重构安全检查系统
 * 确保重构操作的安全性和可回滚性
 */

import * as fs from "fs";
import * as path from "path";
import { CodebaseAnalysis, FileAnalysis } from "../analysis/types.js";
import { RefactorSuggestion, RefactorImpact, RiskLevel } from "./types.js";

/**
 * 重构安全检查结果
 */
export interface RefactorSafetyCheck {
  /** 是否安全 */
  isSafe: boolean;
  /** 风险级别 */
  riskLevel: RiskLevel;
  /** 安全检查项 */
  checks: SafetyCheckItem[];
  /** 预防措施 */
  preventiveMeasures: PreventiveMeasure[];
  /** 回滚计划 */
  rollbackPlan: RollbackPlan;
}

/**
 * 安全检查项
 */
export interface SafetyCheckItem {
  /** 检查名称 */
  name: string;
  /** 检查结果 */
  passed: boolean;
  /** 检查描述 */
  description: string;
  /** 风险级别 */
  riskLevel: RiskLevel;
  /** 建议措施 */
  recommendation?: string;
}

/**
 * 预防措施
 */
export interface PreventiveMeasure {
  /** 措施类型 */
  type: "backup" | "test" | "validation" | "monitoring";
  /** 措施描述 */
  description: string;
  /** 是否必需 */
  required: boolean;
  /** 执行命令 */
  command?: string;
}

/**
 * 回滚计划
 */
export interface RollbackPlan {
  /** 回滚步骤 */
  steps: RollbackStep[];
  /** 预估回滚时间 */
  estimatedTime: string;
  /** 回滚风险 */
  rollbackRisk: RiskLevel;
}

/**
 * 回滚步骤
 */
export interface RollbackStep {
  /** 步骤序号 */
  order: number;
  /** 步骤描述 */
  description: string;
  /** 执行命令 */
  command?: string;
  /** 验证方法 */
  verification?: string;
}

/**
 * 重构安全检查器
 */
export class RefactorSafetyChecker {
  private codebaseAnalysis: CodebaseAnalysis;
  private projectRoot: string;

  constructor(codebaseAnalysis: CodebaseAnalysis, projectRoot: string) {
    this.codebaseAnalysis = codebaseAnalysis;
    this.projectRoot = projectRoot;
  }

  /**
   * 执行重构安全检查
   */
  async performSafetyCheck(suggestion: RefactorSuggestion): Promise<RefactorSafetyCheck> {
    console.log(`🔒 Performing safety check for ${suggestion.type} refactor...`);
    
    const checks: SafetyCheckItem[] = [];
    
    // 1. 基础安全检查
    checks.push(...await this.performBasicSafetyChecks(suggestion));
    
    // 2. 依赖关系检查
    checks.push(...await this.performDependencyChecks(suggestion));
    
    // 3. 测试覆盖检查
    checks.push(...await this.performTestCoverageChecks(suggestion));
    
    // 4. 版本控制检查
    checks.push(...await this.performVersionControlChecks(suggestion));
    
    // 5. 构建系统检查
    checks.push(...await this.performBuildSystemChecks(suggestion));
    
    // 计算整体风险级别
    const riskLevel = this.calculateOverallRisk(checks, suggestion.risk);
    
    // 生成预防措施
    const preventiveMeasures = this.generatePreventiveMeasures(suggestion, checks);
    
    // 生成回滚计划
    const rollbackPlan = this.generateRollbackPlan(suggestion, checks);
    
    // 判断是否安全
    const isSafe = this.determineSafety(checks, riskLevel);
    
    console.log(`✅ Safety check completed - Safe: ${isSafe}, Risk: ${riskLevel}`);
    
    return {
      isSafe,
      riskLevel,
      checks,
      preventiveMeasures,
      rollbackPlan
    };
  }

  /**
   * 基础安全检查
   */
  private async performBasicSafetyChecks(suggestion: RefactorSuggestion): Promise<SafetyCheckItem[]> {
    const checks: SafetyCheckItem[] = [];
    
    // 检查目标文件是否存在
    const targetFileExists = fs.existsSync(path.join(this.projectRoot, suggestion.targetFile));
    checks.push({
      name: "目标文件存在性",
      passed: targetFileExists,
      description: "检查重构目标文件是否存在",
      riskLevel: targetFileExists ? "low" : "high",
      recommendation: targetFileExists ? undefined : "确认文件路径正确"
    });
    
    // 检查文件权限
    if (targetFileExists) {
      try {
        fs.accessSync(path.join(this.projectRoot, suggestion.targetFile), fs.constants.W_OK);
        checks.push({
          name: "文件写入权限",
          passed: true,
          description: "检查目标文件是否可写",
          riskLevel: "low"
        });
      } catch (error) {
        checks.push({
          name: "文件写入权限",
          passed: false,
          description: "检查目标文件是否可写",
          riskLevel: "high",
          recommendation: "确保文件具有写入权限"
        });
      }
    }
    
    // 检查重构类型的复杂度
    const complexRefactorTypes = ["extract_class", "move_method"];
    const isComplexRefactor = complexRefactorTypes.includes(suggestion.type);
    checks.push({
      name: "重构复杂度",
      passed: !isComplexRefactor,
      description: "评估重构操作的复杂度",
      riskLevel: isComplexRefactor ? "medium" : "low",
      recommendation: isComplexRefactor ? "建议分步骤执行重构" : undefined
    });
    
    return checks;
  }

  /**
   * 依赖关系检查
   */
  private async performDependencyChecks(suggestion: RefactorSuggestion): Promise<SafetyCheckItem[]> {
    const checks: SafetyCheckItem[] = [];
    
    // 检查循环依赖风险
    const hasCyclicDependencyRisk = suggestion.impact.affectedFiles.length > 3;
    checks.push({
      name: "循环依赖风险",
      passed: !hasCyclicDependencyRisk,
      description: "检查重构是否可能引入循环依赖",
      riskLevel: hasCyclicDependencyRisk ? "medium" : "low",
      recommendation: hasCyclicDependencyRisk ? "仔细检查模块间依赖关系" : undefined
    });
    
    // 检查外部依赖影响
    const affectsExternalDependencies = this.checkExternalDependencyImpact(suggestion);
    checks.push({
      name: "外部依赖影响",
      passed: !affectsExternalDependencies,
      description: "检查重构是否影响外部依赖",
      riskLevel: affectsExternalDependencies ? "high" : "low",
      recommendation: affectsExternalDependencies ? "确认外部依赖兼容性" : undefined
    });
    
    // 检查 API 接口变更
    const hasAPIChanges = this.checkAPIChanges(suggestion);
    checks.push({
      name: "API 接口变更",
      passed: !hasAPIChanges,
      description: "检查重构是否改变公共 API",
      riskLevel: hasAPIChanges ? "high" : "low",
      recommendation: hasAPIChanges ? "确保 API 向后兼容或提供迁移指南" : undefined
    });
    
    return checks;
  }

  /**
   * 测试覆盖检查
   */
  private async performTestCoverageChecks(suggestion: RefactorSuggestion): Promise<SafetyCheckItem[]> {
    const checks: SafetyCheckItem[] = [];
    
    // 检查测试文件存在性
    const hasTests = this.checkTestFileExists(suggestion.targetFile);
    checks.push({
      name: "测试文件存在",
      passed: hasTests,
      description: "检查目标文件是否有对应的测试",
      riskLevel: hasTests ? "low" : "medium",
      recommendation: hasTests ? undefined : "建议先添加测试再进行重构"
    });
    
    // 检查测试覆盖率
    const testCoverage = this.estimateTestCoverage(suggestion.targetFile);
    const hasGoodCoverage = testCoverage >= 70;
    checks.push({
      name: "测试覆盖率",
      passed: hasGoodCoverage,
      description: `当前测试覆盖率约 ${testCoverage}%`,
      riskLevel: hasGoodCoverage ? "low" : "medium",
      recommendation: hasGoodCoverage ? undefined : "建议提高测试覆盖率到 70% 以上"
    });
    
    // 检查测试影响范围
    const testImpactLevel = suggestion.impact.testImpact;
    const hasMinorTestImpact = testImpactLevel === "none" || testImpactLevel === "minor";
    checks.push({
      name: "测试影响范围",
      passed: hasMinorTestImpact,
      description: `测试影响级别: ${testImpactLevel}`,
      riskLevel: hasMinorTestImpact ? "low" : "medium",
      recommendation: hasMinorTestImpact ? undefined : "需要更新相关测试用例"
    });
    
    return checks;
  }

  /**
   * 版本控制检查
   */
  private async performVersionControlChecks(suggestion: RefactorSuggestion): Promise<SafetyCheckItem[]> {
    const checks: SafetyCheckItem[] = [];
    
    // 检查 Git 状态
    const hasUncommittedChanges = await this.checkUncommittedChanges();
    checks.push({
      name: "版本控制状态",
      passed: !hasUncommittedChanges,
      description: "检查是否有未提交的更改",
      riskLevel: hasUncommittedChanges ? "medium" : "low",
      recommendation: hasUncommittedChanges ? "建议先提交或暂存当前更改" : undefined
    });
    
    // 检查分支状态
    const isOnFeatureBranch = await this.checkBranchStatus();
    checks.push({
      name: "分支安全性",
      passed: isOnFeatureBranch,
      description: "检查是否在安全的分支上进行重构",
      riskLevel: isOnFeatureBranch ? "low" : "medium",
      recommendation: isOnFeatureBranch ? undefined : "建议在功能分支上进行重构"
    });
    
    return checks;
  }

  /**
   * 构建系统检查
   */
  private async performBuildSystemChecks(suggestion: RefactorSuggestion): Promise<SafetyCheckItem[]> {
    const checks: SafetyCheckItem[] = [];
    
    // 检查构建配置
    const hasBuildConfig = this.checkBuildConfiguration();
    checks.push({
      name: "构建配置",
      passed: hasBuildConfig,
      description: "检查项目构建配置是否完整",
      riskLevel: hasBuildConfig ? "low" : "medium",
      recommendation: hasBuildConfig ? undefined : "确保构建配置正确"
    });
    
    // 检查 CI/CD 状态
    const hasCIConfig = this.checkCIConfiguration();
    checks.push({
      name: "CI/CD 配置",
      passed: hasCIConfig,
      description: "检查持续集成配置",
      riskLevel: hasCIConfig ? "low" : "low", // CI 不是必需的，但有更好
      recommendation: hasCIConfig ? undefined : "建议配置 CI/CD 以自动验证重构"
    });
    
    return checks;
  }

  /**
   * 计算整体风险级别
   */
  private calculateOverallRisk(checks: SafetyCheckItem[], suggestionRisk: RiskLevel): RiskLevel {
    const riskScores = { low: 1, medium: 2, high: 3, critical: 4 };
    
    // 计算检查项的平均风险
    const checkRiskSum = checks.reduce((sum, check) => sum + riskScores[check.riskLevel], 0);
    const avgCheckRisk = checkRiskSum / checks.length;
    
    // 结合建议本身的风险
    const suggestionRiskScore = riskScores[suggestionRisk];
    
    // 计算综合风险
    const overallRiskScore = Math.max(avgCheckRisk, suggestionRiskScore);
    
    if (overallRiskScore <= 1.5) return "low";
    if (overallRiskScore <= 2.5) return "medium";
    if (overallRiskScore <= 3.5) return "high";
    return "critical";
  }

  /**
   * 生成预防措施
   */
  private generatePreventiveMeasures(suggestion: RefactorSuggestion, checks: SafetyCheckItem[]): PreventiveMeasure[] {
    const measures: PreventiveMeasure[] = [];
    
    // 代码备份
    measures.push({
      type: "backup",
      description: "创建代码备份",
      required: true,
      command: `git stash push -m "Pre-refactor backup: ${suggestion.type}"`
    });
    
    // 运行测试
    if (checks.some(c => c.name.includes("测试"))) {
      measures.push({
        type: "test",
        description: "运行现有测试确保基线正确",
        required: true,
        command: "npm test"
      });
    }
    
    // 代码验证
    measures.push({
      type: "validation",
      description: "验证代码语法和类型检查",
      required: true,
      command: "npm run lint && npm run type-check"
    });
    
    // 构建验证
    if (suggestion.impact.breakingChanges) {
      measures.push({
        type: "validation",
        description: "验证构建成功",
        required: true,
        command: "npm run build"
      });
    }
    
    return measures;
  }

  /**
   * 生成回滚计划
   */
  private generateRollbackPlan(suggestion: RefactorSuggestion, checks: SafetyCheckItem[]): RollbackPlan {
    const steps: RollbackStep[] = [];
    
    // 基本回滚步骤
    steps.push({
      order: 1,
      description: "恢复代码备份",
      command: "git stash pop",
      verification: "检查文件内容是否恢复"
    });
    
    if (suggestion.impact.breakingChanges) {
      steps.push({
        order: 2,
        description: "重新构建项目",
        command: "npm run build",
        verification: "确认构建成功"
      });
      
      steps.push({
        order: 3,
        description: "运行完整测试套件",
        command: "npm test",
        verification: "确认所有测试通过"
      });
    }
    
    // 评估回滚风险
    const rollbackRisk = suggestion.impact.breakingChanges ? "medium" : "low";
    
    return {
      steps,
      estimatedTime: steps.length <= 2 ? "5-10 分钟" : "15-30 分钟",
      rollbackRisk
    };
  }

  /**
   * 判断重构是否安全
   */
  private determineSafety(checks: SafetyCheckItem[], riskLevel: RiskLevel): boolean {
    // 如果有任何高风险或关键风险的检查项失败，则不安全
    const hasHighRiskFailures = checks.some(check => 
      !check.passed && (check.riskLevel === "high" || check.riskLevel === "critical")
    );
    
    if (hasHighRiskFailures) return false;
    
    // 如果整体风险级别过高，则不安全
    if (riskLevel === "critical") return false;
    
    // 计算失败检查项的比例
    const failedChecks = checks.filter(check => !check.passed).length;
    const failureRate = failedChecks / checks.length;
    
    // 如果失败率超过 30%，则不安全
    return failureRate <= 0.3;
  }

  // ============================================================================
  // 辅助检查方法
  // ============================================================================

  private checkExternalDependencyImpact(suggestion: RefactorSuggestion): boolean {
    // 简化实现：检查是否影响 package.json 或 node_modules
    return suggestion.impact.affectedFiles.some(file => 
      file.includes("package.json") || file.includes("node_modules")
    );
  }

  private checkAPIChanges(suggestion: RefactorSuggestion): boolean {
    // 简化实现：检查是否是可能影响 API 的重构类型
    const apiImpactingTypes = ["rename", "move_method", "extract_class"];
    return apiImpactingTypes.includes(suggestion.type);
  }

  private checkTestFileExists(targetFile: string): boolean {
    const testPatterns = [
      targetFile.replace(/\.(ts|js)$/, ".test.$1"),
      targetFile.replace(/\.(ts|js)$/, ".spec.$1"),
      targetFile.replace(/src\//, "test/").replace(/\.(ts|js)$/, ".test.$1")
    ];
    
    return testPatterns.some(pattern => 
      fs.existsSync(path.join(this.projectRoot, pattern))
    );
  }

  private estimateTestCoverage(targetFile: string): number {
    // 简化实现：基于测试文件存在性估算覆盖率
    const hasTests = this.checkTestFileExists(targetFile);
    return hasTests ? 75 : 25; // 简化的估算
  }

  private async checkUncommittedChanges(): Promise<boolean> {
    try {
      // 简化实现：检查 git status
      const { execSync } = require("child_process");
      const output = execSync("git status --porcelain", { 
        cwd: this.projectRoot,
        encoding: "utf8"
      });
      return output.trim().length > 0;
    } catch (error) {
      return false; // 如果不是 git 仓库，假设没有未提交更改
    }
  }

  private async checkBranchStatus(): Promise<boolean> {
    try {
      const { execSync } = require("child_process");
      const branch = execSync("git branch --show-current", {
        cwd: this.projectRoot,
        encoding: "utf8"
      }).trim();
      
      // 检查是否在主分支上（不安全）
      const mainBranches = ["main", "master", "develop"];
      return !mainBranches.includes(branch);
    } catch (error) {
      return true; // 如果无法检查，假设安全
    }
  }

  private checkBuildConfiguration(): boolean {
    const buildFiles = [
      "package.json",
      "tsconfig.json",
      "webpack.config.js",
      "vite.config.js",
      "rollup.config.js"
    ];
    
    return buildFiles.some(file => 
      fs.existsSync(path.join(this.projectRoot, file))
    );
  }

  private checkCIConfiguration(): boolean {
    const ciFiles = [
      ".github/workflows",
      ".gitlab-ci.yml",
      "azure-pipelines.yml",
      "Jenkinsfile"
    ];
    
    return ciFiles.some(file => 
      fs.existsSync(path.join(this.projectRoot, file))
    );
  }
}
