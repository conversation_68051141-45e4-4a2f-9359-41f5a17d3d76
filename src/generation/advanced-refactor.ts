/**
 * 智能代码生成 - 高级重构分析器
 * 深度集成预测分析结果的智能重构系统
 */

import { CodebaseAnalysis, FileAnalysis, FunctionInfo, ClassInfo } from "../analysis/types.js";
import { PredictionAnalysis, IssueType, PerformanceBottleneck, SecurityVulnerability, TechnicalDebt } from "../prediction/types.js";
import { RefactorSuggestion, RefactorType, RefactorImpact, RiskLevel } from "./types.js";

/**
 * 高级重构分析器
 */
export class AdvancedRefactorAnalyzer {
  private codebaseAnalysis: CodebaseAnalysis;
  private predictionAnalysis: PredictionAnalysis;

  constructor(codebaseAnalysis: CodebaseAnalysis, predictionAnalysis: PredictionAnalysis) {
    this.codebaseAnalysis = codebaseAnalysis;
    this.predictionAnalysis = predictionAnalysis;
  }

  /**
   * 生成智能重构建议
   */
  async generateIntelligentRefactorSuggestions(targetFile?: string): Promise<RefactorSuggestion[]> {
    console.log(`🔄 Generating intelligent refactor suggestions${targetFile ? ` for ${targetFile}` : ''}...`);
    
    const suggestions: RefactorSuggestion[] = [];
    
    // 1. 基于问题预测的重构建议
    const issueDrivenSuggestions = await this.generateIssueDrivenRefactors(targetFile);
    suggestions.push(...issueDrivenSuggestions);
    
    // 2. 基于性能预测的重构建议
    const performanceDrivenSuggestions = await this.generatePerformanceDrivenRefactors(targetFile);
    suggestions.push(...performanceDrivenSuggestions);
    
    // 3. 基于安全扫描的重构建议
    const securityDrivenSuggestions = await this.generateSecurityDrivenRefactors(targetFile);
    suggestions.push(...securityDrivenSuggestions);
    
    // 4. 基于技术债务的重构建议
    const debtDrivenSuggestions = await this.generateDebtDrivenRefactors(targetFile);
    suggestions.push(...debtDrivenSuggestions);
    
    // 5. 基于代码质量的重构建议
    const qualityDrivenSuggestions = await this.generateQualityDrivenRefactors(targetFile);
    suggestions.push(...qualityDrivenSuggestions);
    
    // 6. 智能排序和优先级分析
    const prioritizedSuggestions = await this.prioritizeRefactorSuggestions(suggestions);
    
    console.log(`✅ Generated ${prioritizedSuggestions.length} intelligent refactor suggestions`);
    return prioritizedSuggestions;
  }

  /**
   * 基于问题预测生成重构建议
   */
  private async generateIssueDrivenRefactors(targetFile?: string): Promise<RefactorSuggestion[]> {
    const suggestions: RefactorSuggestion[] = [];
    
    for (const issue of this.predictionAnalysis.issues) {
      // 过滤目标文件
      if (targetFile && issue.location?.file !== targetFile) continue;
      
      switch (issue.type) {
        case 'bug_prone_code':
          suggestions.push(await this.createExtractMethodForBugProne(issue));
          break;
        case 'maintainability_decline':
          suggestions.push(await this.createClassExtractionForMaintainability(issue));
          break;
        case 'code_duplication':
          suggestions.push(await this.createConsolidationForDuplication(issue));
          break;
        case 'complexity_growth':
          suggestions.push(await this.createComplexityReduction(issue));
          break;
        case 'coupling_increase':
          suggestions.push(await this.createDecouplingRefactor(issue));
          break;
        case 'naming_inconsistency':
          suggestions.push(await this.createNamingRefactor(issue));
          break;
      }
    }
    
    return suggestions.filter(s => s !== null);
  }

  /**
   * 基于性能预测生成重构建议
   */
  private async generatePerformanceDrivenRefactors(targetFile?: string): Promise<RefactorSuggestion[]> {
    const suggestions: RefactorSuggestion[] = [];
    
    for (const bottleneck of this.predictionAnalysis.performance.bottlenecks) {
      // 过滤目标文件
      if (targetFile && bottleneck.location?.file !== targetFile) continue;
      
      switch (bottleneck.type) {
        case 'algorithm':
          suggestions.push(await this.createAlgorithmOptimizationRefactor(bottleneck));
          break;
        case 'memory':
          suggestions.push(await this.createMemoryOptimizationRefactor(bottleneck));
          break;
        case 'io':
          suggestions.push(await this.createIOOptimizationRefactor(bottleneck));
          break;
        case 'database':
          suggestions.push(await this.createDatabaseOptimizationRefactor(bottleneck));
          break;
      }
    }
    
    return suggestions.filter(s => s !== null);
  }

  /**
   * 基于安全扫描生成重构建议
   */
  private async generateSecurityDrivenRefactors(targetFile?: string): Promise<RefactorSuggestion[]> {
    const suggestions: RefactorSuggestion[] = [];
    
    for (const vulnerability of this.predictionAnalysis.security.vulnerabilities) {
      // 过滤目标文件
      if (targetFile && vulnerability.location?.file !== targetFile) continue;
      
      switch (vulnerability.type) {
        case 'sql_injection':
          suggestions.push(await this.createSQLInjectionRefactor(vulnerability));
          break;
        case 'xss':
          suggestions.push(await this.createXSSRefactor(vulnerability));
          break;
        case 'insecure_crypto':
          suggestions.push(await this.createCryptoRefactor(vulnerability));
          break;
        case 'auth_bypass':
          suggestions.push(await this.createAuthRefactor(vulnerability));
          break;
      }
    }
    
    return suggestions.filter(s => s !== null);
  }

  /**
   * 基于技术债务生成重构建议
   */
  private async generateDebtDrivenRefactors(targetFile?: string): Promise<RefactorSuggestion[]> {
    const suggestions: RefactorSuggestion[] = [];
    
    for (const debt of this.predictionAnalysis.technicalDebt.debts) {
      // 过滤目标文件
      if (targetFile && debt.location?.file !== targetFile) continue;
      
      switch (debt.type) {
        case 'code_smell':
          suggestions.push(await this.createCodeSmellRefactor(debt));
          break;
        case 'design_debt':
          suggestions.push(await this.createDesignDebtRefactor(debt));
          break;
        case 'documentation_debt':
          suggestions.push(await this.createDocumentationRefactor(debt));
          break;
        case 'test_debt':
          suggestions.push(await this.createTestDebtRefactor(debt));
          break;
      }
    }
    
    return suggestions.filter(s => s !== null);
  }

  /**
   * 基于代码质量生成重构建议
   */
  private async generateQualityDrivenRefactors(targetFile?: string): Promise<RefactorSuggestion[]> {
    const suggestions: RefactorSuggestion[] = [];
    
    const filesToAnalyze = targetFile 
      ? this.codebaseAnalysis.files.filter(f => f.path === targetFile)
      : this.codebaseAnalysis.files;
    
    for (const file of filesToAnalyze) {
      // 分析函数复杂度
      if (file.functions) {
        for (const func of file.functions) {
          if (func.complexity > 15) {
            suggestions.push(await this.createComplexFunctionRefactor(func, file));
          }
        }
      }
      
      // 分析类大小
      if (file.classes) {
        for (const cls of file.classes) {
          if (cls.methods && cls.methods.length > 20) {
            suggestions.push(await this.createLargeClassRefactor(cls, file));
          }
        }
      }
      
      // 分析方法长度
      const longMethods = this.findLongMethods(file);
      for (const method of longMethods) {
        suggestions.push(await this.createLongMethodRefactor(method, file));
      }
    }
    
    return suggestions.filter(s => s !== null);
  }

  /**
   * 智能排序重构建议
   */
  private async prioritizeRefactorSuggestions(suggestions: RefactorSuggestion[]): Promise<RefactorSuggestion[]> {
    return suggestions.sort((a, b) => {
      // 1. 按风险级别排序（低风险优先）
      const riskOrder = { low: 0, medium: 1, high: 2, critical: 3 };
      const riskDiff = riskOrder[a.risk] - riskOrder[b.risk];
      if (riskDiff !== 0) return riskDiff;
      
      // 2. 按预期收益排序（收益多的优先）
      const benefitDiff = b.benefits.length - a.benefits.length;
      if (benefitDiff !== 0) return benefitDiff;
      
      // 3. 按影响范围排序（影响小的优先）
      const impactDiff = a.impact.affectedFiles.length - b.impact.affectedFiles.length;
      if (impactDiff !== 0) return impactDiff;
      
      // 4. 按破坏性变更排序（非破坏性优先）
      if (a.impact.breakingChanges !== b.impact.breakingChanges) {
        return a.impact.breakingChanges ? 1 : -1;
      }
      
      return 0;
    });
  }

  // ============================================================================
  // 具体重构建议创建方法
  // ============================================================================

  /**
   * 为易出错代码创建提取方法建议
   */
  private async createExtractMethodForBugProne(issue: any): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(issue.location?.file, ['extract_method']);
    
    return {
      type: "extract_method",
      targetFile: issue.location?.file || "unknown",
      description: `提取易出错的复杂方法以降低 Bug 风险`,
      beforeCode: this.generateBeforeCode(issue, 'complex_method'),
      afterCode: this.generateAfterCode(issue, 'extracted_methods'),
      impact,
      risk: this.calculateRisk(impact, issue.severity),
      benefits: [
        "降低 Bug 出现概率",
        "提高代码可读性",
        "增强可测试性",
        "便于单独调试"
      ]
    };
  }

  /**
   * 为可维护性问题创建类提取建议
   */
  private async createClassExtractionForMaintainability(issue: any): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(issue.location?.file, ['extract_class']);
    
    return {
      type: "extract_class",
      targetFile: issue.location?.file || "unknown",
      description: `拆分职责过多的类以提高可维护性`,
      beforeCode: this.generateBeforeCode(issue, 'large_class'),
      afterCode: this.generateAfterCode(issue, 'split_classes'),
      impact,
      risk: this.calculateRisk(impact, issue.severity),
      benefits: [
        "遵循单一职责原则",
        "提高代码内聚性",
        "降低维护复杂度",
        "便于功能扩展"
      ]
    };
  }

  /**
   * 为重复代码创建合并建议
   */
  private async createConsolidationForDuplication(issue: any): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(issue.location?.file, ['consolidate_duplicate']);
    
    return {
      type: "consolidate_duplicate",
      targetFile: issue.location?.file || "unknown",
      description: `合并重复代码以减少维护成本`,
      beforeCode: this.generateBeforeCode(issue, 'duplicate_code'),
      afterCode: this.generateAfterCode(issue, 'consolidated_code'),
      impact,
      risk: "low" as RiskLevel,
      benefits: [
        "减少代码重复",
        "降低维护成本",
        "提高一致性",
        "便于统一修改"
      ]
    };
  }

  /**
   * 分析重构影响
   */
  private async analyzeRefactorImpact(filePath: string, refactorTypes: string[]): Promise<RefactorImpact> {
    const file = this.codebaseAnalysis.files.find(f => f.path === filePath);
    if (!file) {
      return {
        affectedFiles: [filePath],
        affectedFunctions: [],
        affectedClasses: [],
        breakingChanges: false,
        testImpact: "none"
      };
    }
    
    // 分析依赖关系
    const dependentFiles = this.findDependentFiles(filePath);
    
    // 评估破坏性变更
    const hasBreakingChanges = refactorTypes.some(type => 
      ['extract_class', 'move_method', 'rename'].includes(type)
    );
    
    // 评估测试影响
    const testImpact = this.assessTestImpact(file, refactorTypes);
    
    return {
      affectedFiles: [filePath, ...dependentFiles],
      affectedFunctions: file.functions?.map(f => f.name) || [],
      affectedClasses: file.classes?.map(c => c.name) || [],
      breakingChanges: hasBreakingChanges,
      testImpact
    };
  }

  /**
   * 计算重构风险
   */
  private calculateRisk(impact: RefactorImpact, issueSeverity?: string): RiskLevel {
    let riskScore = 0;
    
    // 基于影响文件数量
    riskScore += Math.min(impact.affectedFiles.length * 0.2, 1);
    
    // 基于破坏性变更
    if (impact.breakingChanges) riskScore += 0.5;
    
    // 基于测试影响
    const testImpactScore = {
      none: 0,
      minor: 0.1,
      major: 0.3,
      complete_rewrite: 0.5
    };
    riskScore += testImpactScore[impact.testImpact] || 0;
    
    // 基于问题严重程度
    const severityScore = {
      low: 0,
      medium: 0.1,
      high: 0.2,
      critical: 0.3
    };
    riskScore += severityScore[issueSeverity as keyof typeof severityScore] || 0;
    
    // 转换为风险级别
    if (riskScore <= 0.3) return "low";
    if (riskScore <= 0.6) return "medium";
    if (riskScore <= 0.8) return "high";
    return "critical";
  }

  // ============================================================================
  // 辅助方法
  // ============================================================================

  private findDependentFiles(filePath: string): string[] {
    // 简化实现，实际需要分析 import/require 关系
    return this.codebaseAnalysis.files
      .filter(f => f.imports?.some(imp => imp.includes(filePath)))
      .map(f => f.path);
  }

  private assessTestImpact(file: FileAnalysis, refactorTypes: string[]): "none" | "minor" | "major" | "complete_rewrite" {
    // 简化实现
    if (refactorTypes.includes('extract_class')) return "major";
    if (refactorTypes.includes('rename')) return "minor";
    return "none";
  }

  private findLongMethods(file: FileAnalysis): any[] {
    // 简化实现，实际需要分析方法行数
    return file.functions?.filter(f => f.complexity > 10) || [];
  }

  private generateBeforeCode(issue: any, type: string): string {
    // 简化实现，实际需要从代码库中提取真实代码
    const templates = {
      complex_method: `// 复杂且易出错的方法
function processUserData(userData) {
  // 50+ 行复杂逻辑
  // 多个职责混合
  // 难以测试和维护
}`,
      large_class: `// 职责过多的大类
class UserManager {
  // 用户管理 + 邮件发送 + 数据验证 + 报告生成
  // 20+ 个方法，违反单一职责原则
}`,
      duplicate_code: `// 重复的代码块
function validateUser(user) {
  if (!user.email) throw new Error('Email required');
  if (!user.name) throw new Error('Name required');
  // 处理逻辑...
}

function validateAdmin(admin) {
  if (!admin.email) throw new Error('Email required');
  if (!admin.name) throw new Error('Name required');
  // 处理逻辑...
}`
    };
    
    return templates[type as keyof typeof templates] || "// 待重构的代码";
  }

  private generateAfterCode(issue: any, type: string): string {
    // 简化实现
    const templates = {
      extracted_methods: `// 重构后的方法
function processUserData(userData) {
  const validatedData = validateUserData(userData);
  const processedData = transformUserData(validatedData);
  return saveUserData(processedData);
}

function validateUserData(userData) {
  // 验证逻辑
}

function transformUserData(userData) {
  // 转换逻辑
}

function saveUserData(userData) {
  // 保存逻辑
}`,
      split_classes: `// 拆分后的类
class UserManager {
  constructor(
    private emailService: EmailService,
    private validator: UserValidator,
    private reporter: UserReporter
  ) {}
  
  // 只负责用户管理
}

class EmailService {
  // 只负责邮件发送
}

class UserValidator {
  // 只负责数据验证
}

class UserReporter {
  // 只负责报告生成
}`,
      consolidated_code: `// 提取公共验证逻辑
function validateRequiredFields(data: { email?: string; name?: string }) {
  if (!data.email) throw new Error('Email required');
  if (!data.name) throw new Error('Name required');
}

function validateUser(user) {
  validateRequiredFields(user);
  // 用户特定逻辑...
}

function validateAdmin(admin) {
  validateRequiredFields(admin);
  // 管理员特定逻辑...
}`
    };
    
    return templates[type as keyof typeof templates] || "// 重构后的代码";
  }

  // 其他重构方法的占位符实现
  private async createComplexityReduction(issue: any): Promise<RefactorSuggestion> {
    return this.createExtractMethodForBugProne(issue);
  }

  private async createDecouplingRefactor(issue: any): Promise<RefactorSuggestion> {
    return this.createClassExtractionForMaintainability(issue);
  }

  private async createNamingRefactor(issue: any): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(issue.location?.file, ['rename']);
    return {
      type: "rename",
      targetFile: issue.location?.file || "unknown",
      description: "重命名以提高代码可读性",
      beforeCode: "// 命名不清晰的代码",
      afterCode: "// 重命名后的代码",
      impact,
      risk: "low",
      benefits: ["提高代码可读性", "增强代码自文档化"]
    };
  }

  private async createAlgorithmOptimizationRefactor(bottleneck: PerformanceBottleneck): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(bottleneck.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: bottleneck.location?.file || "unknown",
      description: "优化算法以提高性能",
      beforeCode: "// 低效算法实现",
      afterCode: "// 优化后的算法实现",
      impact,
      risk: "medium",
      benefits: ["提高执行性能", "减少资源消耗"]
    };
  }

  private async createMemoryOptimizationRefactor(bottleneck: PerformanceBottleneck): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(bottleneck.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: bottleneck.location?.file || "unknown",
      description: "优化内存使用",
      beforeCode: "// 内存使用较多的实现",
      afterCode: "// 内存优化后的实现",
      impact,
      risk: "low",
      benefits: ["减少内存占用", "提高性能"]
    };
  }

  private async createIOOptimizationRefactor(bottleneck: PerformanceBottleneck): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(bottleneck.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: bottleneck.location?.file || "unknown",
      description: "优化 I/O 操作",
      beforeCode: "// 低效的 I/O 操作",
      afterCode: "// 优化后的 I/O 操作",
      impact,
      risk: "medium",
      benefits: ["提高 I/O 性能", "减少阻塞时间"]
    };
  }

  private async createDatabaseOptimizationRefactor(bottleneck: PerformanceBottleneck): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(bottleneck.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: bottleneck.location?.file || "unknown",
      description: "优化数据库查询",
      beforeCode: "// 低效的数据库查询",
      afterCode: "// 优化后的数据库查询",
      impact,
      risk: "medium",
      benefits: ["提高查询性能", "减少数据库负载"]
    };
  }

  private async createSQLInjectionRefactor(vulnerability: SecurityVulnerability): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(vulnerability.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: vulnerability.location?.file || "unknown",
      description: "修复 SQL 注入漏洞",
      beforeCode: "// 存在 SQL 注入风险的代码",
      afterCode: "// 使用参数化查询的安全代码",
      impact,
      risk: "high",
      benefits: ["消除 SQL 注入风险", "提高数据安全性"]
    };
  }

  private async createXSSRefactor(vulnerability: SecurityVulnerability): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(vulnerability.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: vulnerability.location?.file || "unknown",
      description: "修复 XSS 漏洞",
      beforeCode: "// 存在 XSS 风险的代码",
      afterCode: "// 输入验证和转义后的安全代码",
      impact,
      risk: "high",
      benefits: ["消除 XSS 风险", "提高应用安全性"]
    };
  }

  private async createCryptoRefactor(vulnerability: SecurityVulnerability): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(vulnerability.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: vulnerability.location?.file || "unknown",
      description: "修复加密安全问题",
      beforeCode: "// 不安全的加密实现",
      afterCode: "// 安全的加密实现",
      impact,
      risk: "high",
      benefits: ["提高加密安全性", "符合安全标准"]
    };
  }

  private async createAuthRefactor(vulnerability: SecurityVulnerability): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(vulnerability.location?.file || "unknown", ['replace_conditional']);
    return {
      type: "replace_conditional",
      targetFile: vulnerability.location?.file || "unknown",
      description: "修复认证绕过问题",
      beforeCode: "// 存在认证绕过风险的代码",
      afterCode: "// 安全的认证实现",
      impact,
      risk: "critical",
      benefits: ["消除认证绕过风险", "加强访问控制"]
    };
  }

  private async createCodeSmellRefactor(debt: TechnicalDebt): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(debt.location?.file || "unknown", ['extract_method']);
    return {
      type: "extract_method",
      targetFile: debt.location?.file || "unknown",
      description: "清理代码异味",
      beforeCode: "// 存在代码异味的实现",
      afterCode: "// 清理后的干净代码",
      impact,
      risk: "low",
      benefits: ["提高代码质量", "增强可维护性"]
    };
  }

  private async createDesignDebtRefactor(debt: TechnicalDebt): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(debt.location?.file || "unknown", ['extract_class']);
    return {
      type: "extract_class",
      targetFile: debt.location?.file || "unknown",
      description: "改善设计结构",
      beforeCode: "// 设计不良的代码结构",
      afterCode: "// 改善后的设计结构",
      impact,
      risk: "medium",
      benefits: ["改善代码设计", "提高架构质量"]
    };
  }

  private async createDocumentationRefactor(debt: TechnicalDebt): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(debt.location?.file || "unknown", ['rename']);
    return {
      type: "rename",
      targetFile: debt.location?.file || "unknown",
      description: "完善代码文档",
      beforeCode: "// 缺乏文档的代码",
      afterCode: "// 添加完整文档的代码",
      impact,
      risk: "low",
      benefits: ["提高代码可读性", "便于团队协作"]
    };
  }

  private async createTestDebtRefactor(debt: TechnicalDebt): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(debt.location?.file || "unknown", ['extract_method']);
    return {
      type: "extract_method",
      targetFile: debt.location?.file || "unknown",
      description: "改善测试覆盖",
      beforeCode: "// 缺乏测试的代码",
      afterCode: "// 添加测试的代码",
      impact,
      risk: "low",
      benefits: ["提高测试覆盖率", "增强代码可靠性"]
    };
  }

  private async createComplexFunctionRefactor(func: FunctionInfo, file: FileAnalysis): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(file.path, ['extract_method']);
    return {
      type: "extract_method",
      targetFile: file.path,
      description: `重构复杂函数 ${func.name} (复杂度: ${func.complexity})`,
      beforeCode: `// 复杂函数: ${func.name}`,
      afterCode: `// 重构后的简化函数`,
      impact,
      risk: "low",
      benefits: ["降低函数复杂度", "提高可读性", "增强可测试性"]
    };
  }

  private async createLargeClassRefactor(cls: ClassInfo, file: FileAnalysis): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(file.path, ['extract_class']);
    return {
      type: "extract_class",
      targetFile: file.path,
      description: `拆分大类 ${cls.name} (${cls.methods?.length || 0} 个方法)`,
      beforeCode: `// 大类: ${cls.name}`,
      afterCode: `// 拆分后的类`,
      impact,
      risk: "medium",
      benefits: ["遵循单一职责原则", "提高内聚性", "便于维护"]
    };
  }

  private async createLongMethodRefactor(method: any, file: FileAnalysis): Promise<RefactorSuggestion> {
    const impact = await this.analyzeRefactorImpact(file.path, ['extract_method']);
    return {
      type: "extract_method",
      targetFile: file.path,
      description: `重构长方法 ${method.name}`,
      beforeCode: `// 长方法: ${method.name}`,
      afterCode: `// 重构后的短方法`,
      impact,
      risk: "low",
      benefits: ["提高方法可读性", "便于理解和维护"]
    };
  }
}
