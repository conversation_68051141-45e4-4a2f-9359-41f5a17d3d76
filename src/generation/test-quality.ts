/**
 * 智能代码生成 - 测试质量评估系统
 * 评估生成测试的质量并提供改进建议
 */

import { CodebaseAnalysis, FileAnalysis } from "../analysis/types.js";
import { GeneratedTestSuite, TestCase, TestAssertion } from "./test-generator.js";
import { TestFramework, TestType } from "./types.js";

/**
 * 测试质量评估结果
 */
export interface TestQualityAssessment {
  /** 总体质量分数 (0-100) */
  overallScore: number;
  /** 质量等级 */
  grade: "A" | "B" | "C" | "D" | "F";
  /** 各维度评分 */
  dimensions: TestQualityDimensions;
  /** 覆盖率分析 */
  coverage: CoverageAnalysis;
  /** 改进建议 */
  improvements: string[];
  /** 质量报告 */
  report: TestQualityReport;
}

/**
 * 测试质量维度
 */
export interface TestQualityDimensions {
  /** 完整性 (0-1) */
  completeness: number;
  /** 正确性 (0-1) */
  correctness: number;
  /** 可维护性 (0-1) */
  maintainability: number;
  /** 可读性 (0-1) */
  readability: number;
  /** 有效性 (0-1) */
  effectiveness: number;
}

/**
 * 覆盖率分析
 */
export interface CoverageAnalysis {
  /** 函数覆盖率 */
  functionCoverage: number;
  /** 分支覆盖率 */
  branchCoverage: number;
  /** 语句覆盖率 */
  statementCoverage: number;
  /** 条件覆盖率 */
  conditionCoverage: number;
  /** 未覆盖的函数 */
  uncoveredFunctions: string[];
  /** 未覆盖的分支 */
  uncoveredBranches: string[];
}

/**
 * 测试质量报告
 */
export interface TestQualityReport {
  /** 测试统计 */
  statistics: TestStatistics;
  /** 质量亮点 */
  highlights: string[];
  /** 质量问题 */
  issues: TestQualityIssue[];
  /** 最佳实践遵循情况 */
  bestPractices: BestPracticeCompliance[];
}

/**
 * 测试统计
 */
export interface TestStatistics {
  /** 测试用例总数 */
  totalTestCases: number;
  /** 单元测试数量 */
  unitTests: number;
  /** 集成测试数量 */
  integrationTests: number;
  /** 端到端测试数量 */
  e2eTests: number;
  /** 平均断言数量 */
  averageAssertions: number;
  /** 模拟使用率 */
  mockUsageRate: number;
}

/**
 * 测试质量问题
 */
export interface TestQualityIssue {
  /** 问题类型 */
  type: "missing_test" | "weak_assertion" | "poor_naming" | "excessive_mocking" | "low_coverage";
  /** 问题描述 */
  description: string;
  /** 严重程度 */
  severity: "low" | "medium" | "high" | "critical";
  /** 位置信息 */
  location?: string;
  /** 修复建议 */
  suggestion: string;
}

/**
 * 最佳实践遵循情况
 */
export interface BestPracticeCompliance {
  /** 实践名称 */
  practice: string;
  /** 遵循程度 (0-1) */
  compliance: number;
  /** 描述 */
  description: string;
  /** 改进建议 */
  improvement?: string;
}

/**
 * 测试质量评估器
 */
export class TestQualityAssessor {
  private codebaseAnalysis: CodebaseAnalysis;

  constructor(codebaseAnalysis: CodebaseAnalysis) {
    this.codebaseAnalysis = codebaseAnalysis;
  }

  /**
   * 评估测试质量
   */
  async assessTestQuality(testSuite: GeneratedTestSuite): Promise<TestQualityAssessment> {
    console.log(`📊 Assessing test quality for ${testSuite.targetFile}...`);
    
    // 1. 计算各维度评分
    const dimensions = await this.calculateQualityDimensions(testSuite);
    
    // 2. 分析覆盖率
    const coverage = await this.analyzeCoverage(testSuite);
    
    // 3. 生成质量报告
    const report = await this.generateQualityReport(testSuite, dimensions, coverage);
    
    // 4. 计算总体分数
    const overallScore = this.calculateOverallScore(dimensions, coverage);
    
    // 5. 确定质量等级
    const grade = this.calculateGrade(overallScore);
    
    // 6. 生成改进建议
    const improvements = await this.generateImprovements(testSuite, dimensions, coverage, report);
    
    console.log(`✅ Test quality assessment complete - Grade: ${grade}, Score: ${overallScore}`);
    
    return {
      overallScore,
      grade,
      dimensions,
      coverage,
      improvements,
      report
    };
  }

  /**
   * 计算质量维度评分
   */
  private async calculateQualityDimensions(testSuite: GeneratedTestSuite): Promise<TestQualityDimensions> {
    const completeness = await this.assessCompleteness(testSuite);
    const correctness = await this.assessCorrectness(testSuite);
    const maintainability = await this.assessMaintainability(testSuite);
    const readability = await this.assessReadability(testSuite);
    const effectiveness = await this.assessEffectiveness(testSuite);
    
    return {
      completeness,
      correctness,
      maintainability,
      readability,
      effectiveness
    };
  }

  /**
   * 评估完整性
   */
  private async assessCompleteness(testSuite: GeneratedTestSuite): Promise<number> {
    const targetFile = this.codebaseAnalysis.files.find(f => f.path === testSuite.targetFile);
    if (!targetFile) return 0;
    
    let score = 1.0;
    
    // 检查函数覆盖
    const totalFunctions = targetFile.functions?.length || 0;
    const testedFunctions = this.countTestedFunctions(testSuite, targetFile);
    const functionCoverage = totalFunctions > 0 ? testedFunctions / totalFunctions : 1;
    score *= functionCoverage;
    
    // 检查类覆盖
    const totalClasses = targetFile.classes?.length || 0;
    const testedClasses = this.countTestedClasses(testSuite, targetFile);
    const classCoverage = totalClasses > 0 ? testedClasses / totalClasses : 1;
    score *= classCoverage;
    
    // 检查测试类型覆盖
    const testTypes = new Set(testSuite.testCases.map(tc => tc.type));
    const typeScore = Math.min(testTypes.size / 3, 1); // 最多3种类型
    score *= typeScore;
    
    return Math.max(0, score);
  }

  /**
   * 评估正确性
   */
  private async assessCorrectness(testSuite: GeneratedTestSuite): Promise<number> {
    let score = 1.0;
    
    // 检查断言质量
    const avgAssertions = testSuite.testCases.reduce((sum, tc) => sum + tc.assertions.length, 0) / testSuite.testCases.length;
    if (avgAssertions < 1) score -= 0.3;
    else if (avgAssertions < 2) score -= 0.1;
    
    // 检查测试用例命名
    const poorlyNamedTests = testSuite.testCases.filter(tc => 
      tc.name.length < 10 || !tc.name.includes('should')
    ).length;
    const namingScore = 1 - (poorlyNamedTests / testSuite.testCases.length);
    score *= namingScore;
    
    // 检查边界值测试
    const boundaryTests = testSuite.testCases.filter(tc => 
      tc.inputs.some(input => input.isBoundary)
    ).length;
    const boundaryScore = Math.min(boundaryTests / Math.max(testSuite.testCases.length * 0.3, 1), 1);
    score *= (0.7 + boundaryScore * 0.3);
    
    return Math.max(0, score);
  }

  /**
   * 评估可维护性
   */
  private async assessMaintainability(testSuite: GeneratedTestSuite): Promise<number> {
    let score = 1.0;
    
    // 检查模拟使用
    const totalMocks = testSuite.testCases.reduce((sum, tc) => sum + tc.mocks.length, 0);
    const mockRatio = totalMocks / testSuite.testCases.length;
    if (mockRatio > 3) score -= 0.2; // 过度模拟
    
    // 检查测试独立性
    const dependentTests = this.findDependentTests(testSuite);
    if (dependentTests.length > 0) {
      score -= dependentTests.length * 0.1;
    }
    
    // 检查设置和清理
    const hasProperSetup = testSuite.setup.beforeEach.length > 0 || testSuite.teardown.afterEach.length > 0;
    if (!hasProperSetup && testSuite.testCases.some(tc => tc.mocks.length > 0)) {
      score -= 0.15;
    }
    
    return Math.max(0, score);
  }

  /**
   * 评估可读性
   */
  private async assessReadability(testSuite: GeneratedTestSuite): Promise<number> {
    let score = 1.0;
    
    // 检查测试描述质量
    const wellDescribedTests = testSuite.testCases.filter(tc => 
      tc.description && tc.description.length > 10
    ).length;
    const descriptionScore = wellDescribedTests / testSuite.testCases.length;
    score *= descriptionScore;
    
    // 检查断言描述
    const wellDescribedAssertions = testSuite.testCases.reduce((count, tc) => 
      count + tc.assertions.filter(a => a.description && a.description.length > 5).length, 0
    );
    const totalAssertions = testSuite.testCases.reduce((count, tc) => count + tc.assertions.length, 0);
    const assertionDescriptionScore = totalAssertions > 0 ? wellDescribedAssertions / totalAssertions : 1;
    score *= assertionDescriptionScore;
    
    // 检查测试结构
    const structuredTests = testSuite.testCases.filter(tc => 
      tc.name.includes('should') && tc.description.includes('测试')
    ).length;
    const structureScore = structuredTests / testSuite.testCases.length;
    score *= structureScore;
    
    return Math.max(0, score);
  }

  /**
   * 评估有效性
   */
  private async assessEffectiveness(testSuite: GeneratedTestSuite): Promise<number> {
    let score = 1.0;
    
    // 检查关键路径覆盖
    const criticalTests = testSuite.testCases.filter(tc => tc.priority === "critical" || tc.priority === "high").length;
    const criticalScore = Math.min(criticalTests / Math.max(testSuite.testCases.length * 0.3, 1), 1);
    score *= (0.6 + criticalScore * 0.4);
    
    // 检查异常处理测试
    const exceptionTests = testSuite.testCases.filter(tc => 
      tc.assertions.some(a => a.type === "throws")
    ).length;
    const exceptionScore = Math.min(exceptionTests / Math.max(testSuite.testCases.length * 0.2, 1), 1);
    score *= (0.8 + exceptionScore * 0.2);
    
    // 检查集成测试覆盖
    const integrationTests = testSuite.testCases.filter(tc => tc.type === "integration").length;
    if (integrationTests === 0 && testSuite.testCases.length > 5) {
      score -= 0.1;
    }
    
    return Math.max(0, score);
  }

  /**
   * 分析覆盖率
   */
  private async analyzeCoverage(testSuite: GeneratedTestSuite): Promise<CoverageAnalysis> {
    const targetFile = this.codebaseAnalysis.files.find(f => f.path === testSuite.targetFile);
    if (!targetFile) {
      return {
        functionCoverage: 0,
        branchCoverage: 0,
        statementCoverage: 0,
        conditionCoverage: 0,
        uncoveredFunctions: [],
        uncoveredBranches: []
      };
    }
    
    // 计算函数覆盖率
    const totalFunctions = targetFile.functions?.length || 0;
    const testedFunctions = this.countTestedFunctions(testSuite, targetFile);
    const functionCoverage = totalFunctions > 0 ? testedFunctions / totalFunctions : 1;
    
    // 估算其他覆盖率（简化实现）
    const branchCoverage = functionCoverage * 0.8; // 估算
    const statementCoverage = functionCoverage * 0.9; // 估算
    const conditionCoverage = functionCoverage * 0.7; // 估算
    
    // 找出未覆盖的函数
    const uncoveredFunctions = targetFile.functions?.filter(func => 
      !testSuite.testCases.some(tc => tc.name.includes(func.name))
    ).map(func => func.name) || [];
    
    return {
      functionCoverage: Math.round(functionCoverage * 100) / 100,
      branchCoverage: Math.round(branchCoverage * 100) / 100,
      statementCoverage: Math.round(statementCoverage * 100) / 100,
      conditionCoverage: Math.round(conditionCoverage * 100) / 100,
      uncoveredFunctions,
      uncoveredBranches: [] // 简化实现
    };
  }

  /**
   * 生成质量报告
   */
  private async generateQualityReport(
    testSuite: GeneratedTestSuite,
    dimensions: TestQualityDimensions,
    coverage: CoverageAnalysis
  ): Promise<TestQualityReport> {
    const statistics = this.calculateTestStatistics(testSuite);
    const highlights = this.generateHighlights(testSuite, dimensions, coverage);
    const issues = await this.identifyQualityIssues(testSuite, dimensions, coverage);
    const bestPractices = this.assessBestPractices(testSuite);
    
    return {
      statistics,
      highlights,
      issues,
      bestPractices
    };
  }

  /**
   * 计算总体分数
   */
  private calculateOverallScore(dimensions: TestQualityDimensions, coverage: CoverageAnalysis): number {
    const dimensionScore = (
      dimensions.completeness * 0.25 +
      dimensions.correctness * 0.25 +
      dimensions.maintainability * 0.2 +
      dimensions.readability * 0.15 +
      dimensions.effectiveness * 0.15
    );
    
    const coverageScore = (
      coverage.functionCoverage * 0.4 +
      coverage.branchCoverage * 0.3 +
      coverage.statementCoverage * 0.2 +
      coverage.conditionCoverage * 0.1
    );
    
    const overallScore = dimensionScore * 0.7 + coverageScore * 0.3;
    return Math.round(overallScore * 100);
  }

  /**
   * 计算质量等级
   */
  private calculateGrade(score: number): "A" | "B" | "C" | "D" | "F" {
    if (score >= 90) return "A";
    if (score >= 80) return "B";
    if (score >= 70) return "C";
    if (score >= 60) return "D";
    return "F";
  }

  /**
   * 生成改进建议
   */
  private async generateImprovements(
    testSuite: GeneratedTestSuite,
    dimensions: TestQualityDimensions,
    coverage: CoverageAnalysis,
    report: TestQualityReport
  ): Promise<string[]> {
    const improvements: string[] = [];
    
    // 基于维度评分的建议
    if (dimensions.completeness < 0.8) {
      improvements.push("增加测试用例以提高代码覆盖率");
    }
    
    if (dimensions.correctness < 0.8) {
      improvements.push("改进测试断言的质量和准确性");
    }
    
    if (dimensions.maintainability < 0.8) {
      improvements.push("减少测试间的依赖关系，提高测试独立性");
    }
    
    if (dimensions.readability < 0.8) {
      improvements.push("改善测试用例的命名和描述");
    }
    
    if (dimensions.effectiveness < 0.8) {
      improvements.push("增加关键路径和异常情况的测试覆盖");
    }
    
    // 基于覆盖率的建议
    if (coverage.functionCoverage < 0.8) {
      improvements.push(`为未覆盖的函数添加测试: ${coverage.uncoveredFunctions.join(', ')}`);
    }
    
    // 基于质量问题的建议
    for (const issue of report.issues) {
      if (issue.severity === "high" || issue.severity === "critical") {
        improvements.push(issue.suggestion);
      }
    }
    
    return improvements;
  }

  // ============================================================================
  // 辅助方法
  // ============================================================================

  private countTestedFunctions(testSuite: GeneratedTestSuite, file: FileAnalysis): number {
    const functionNames = file.functions?.map(f => f.name) || [];
    return functionNames.filter(name => 
      testSuite.testCases.some(tc => tc.name.includes(name))
    ).length;
  }

  private countTestedClasses(testSuite: GeneratedTestSuite, file: FileAnalysis): number {
    const classNames = file.classes?.map(c => c.name) || [];
    return classNames.filter(name => 
      testSuite.testCases.some(tc => tc.name.includes(name))
    ).length;
  }

  private findDependentTests(testSuite: GeneratedTestSuite): TestCase[] {
    // 简化实现：检查测试间的依赖
    return testSuite.testCases.filter(tc => 
      tc.name.includes('after') || tc.name.includes('before')
    );
  }

  private calculateTestStatistics(testSuite: GeneratedTestSuite): TestStatistics {
    const totalTestCases = testSuite.testCases.length;
    const unitTests = testSuite.testCases.filter(tc => tc.type === "unit").length;
    const integrationTests = testSuite.testCases.filter(tc => tc.type === "integration").length;
    const e2eTests = testSuite.testCases.filter(tc => tc.type === "e2e").length;
    
    const totalAssertions = testSuite.testCases.reduce((sum, tc) => sum + tc.assertions.length, 0);
    const averageAssertions = totalTestCases > 0 ? totalAssertions / totalTestCases : 0;
    
    const totalMocks = testSuite.testCases.reduce((sum, tc) => sum + tc.mocks.length, 0);
    const mockUsageRate = totalTestCases > 0 ? totalMocks / totalTestCases : 0;
    
    return {
      totalTestCases,
      unitTests,
      integrationTests,
      e2eTests,
      averageAssertions: Math.round(averageAssertions * 100) / 100,
      mockUsageRate: Math.round(mockUsageRate * 100) / 100
    };
  }

  private generateHighlights(
    testSuite: GeneratedTestSuite,
    dimensions: TestQualityDimensions,
    coverage: CoverageAnalysis
  ): string[] {
    const highlights: string[] = [];
    
    if (dimensions.completeness > 0.9) {
      highlights.push("测试覆盖率非常全面");
    }
    
    if (dimensions.correctness > 0.9) {
      highlights.push("测试断言质量很高");
    }
    
    if (coverage.functionCoverage > 0.9) {
      highlights.push("函数覆盖率达到优秀水平");
    }
    
    if (testSuite.testCases.filter(tc => tc.priority === "critical").length > 0) {
      highlights.push("包含关键路径测试");
    }
    
    return highlights;
  }

  private async identifyQualityIssues(
    testSuite: GeneratedTestSuite,
    dimensions: TestQualityDimensions,
    coverage: CoverageAnalysis
  ): Promise<TestQualityIssue[]> {
    const issues: TestQualityIssue[] = [];
    
    // 检查缺失的测试
    if (coverage.uncoveredFunctions.length > 0) {
      issues.push({
        type: "missing_test",
        description: `${coverage.uncoveredFunctions.length} 个函数缺少测试`,
        severity: "medium",
        suggestion: "为所有公共函数添加测试用例"
      });
    }
    
    // 检查弱断言
    const weakAssertionTests = testSuite.testCases.filter(tc => tc.assertions.length < 1);
    if (weakAssertionTests.length > 0) {
      issues.push({
        type: "weak_assertion",
        description: `${weakAssertionTests.length} 个测试缺少断言`,
        severity: "high",
        suggestion: "为每个测试用例添加适当的断言"
      });
    }
    
    // 检查命名问题
    const poorlyNamedTests = testSuite.testCases.filter(tc => tc.name.length < 10);
    if (poorlyNamedTests.length > 0) {
      issues.push({
        type: "poor_naming",
        description: `${poorlyNamedTests.length} 个测试命名不够描述性`,
        severity: "low",
        suggestion: "使用更具描述性的测试名称"
      });
    }
    
    return issues;
  }

  private assessBestPractices(testSuite: GeneratedTestSuite): BestPracticeCompliance[] {
    const practices: BestPracticeCompliance[] = [];
    
    // AAA 模式 (Arrange-Act-Assert)
    const aaaCompliance = testSuite.testCases.filter(tc => 
      tc.assertions.length > 0 && tc.inputs.length >= 0
    ).length / testSuite.testCases.length;
    
    practices.push({
      practice: "AAA 模式",
      compliance: aaaCompliance,
      description: "测试用例遵循 Arrange-Act-Assert 模式",
      improvement: aaaCompliance < 0.8 ? "确保所有测试用例都遵循 AAA 模式" : undefined
    });
    
    // 测试独立性
    const independentTests = testSuite.testCases.filter(tc => tc.mocks.length <= 2).length;
    const independenceCompliance = independentTests / testSuite.testCases.length;
    
    practices.push({
      practice: "测试独立性",
      compliance: independenceCompliance,
      description: "测试用例之间相互独立",
      improvement: independenceCompliance < 0.8 ? "减少测试间的依赖关系" : undefined
    });
    
    return practices;
  }
}
