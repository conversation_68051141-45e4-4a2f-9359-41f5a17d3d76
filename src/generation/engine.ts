/**
 * 智能代码生成引擎 - 核心生成逻辑
 * v1.3.0 核心引擎实现
 */

import { CodebaseAnalysis, FileAnalysis } from "../analysis/types.js";
import { PredictionAnalysis } from "../prediction/types.js";
import {
  CodeGenerationRequest,
  CodeGenerationResult,
  GeneratedCode,
  QualityAssessment,
  GenerationSuggestion,
  GenerationStatistics,
  NextStep,
  GenerationType,
  ProgrammingLanguage
} from "./types.js";
import { extractProjectContext } from "./context.js";
import { getCodeTemplate, renderTemplate } from "./templates.js";
import { assessGeneratedCodeQuality } from "./quality.js";
import { parseCodeDescription, CodeStructure } from "./nlp.js";
import { generateRefactorSuggestions } from "./refactor.js";
import { AdvancedRefactorAnalyzer } from "./advanced-refactor.js";
import { RefactorSafetyChecker } from "./refactor-safety.js";
import { IntelligentTestGenerator } from "./test-generator.js";
import { TestFrameworkAdapterFactory } from "./test-frameworks.js";
import { TestQualityAssessor } from "./test-quality.js";
import { PerformanceAnalyzer } from "./performance-analyzer.js";
import { OptimizationGenerator } from "./optimization-generator.js";

/**
 * 智能代码生成引擎
 */
export class CodeGenerationEngine {
  private codebaseAnalysis: CodebaseAnalysis;
  private predictionAnalysis: PredictionAnalysis;

  constructor(codebaseAnalysis: CodebaseAnalysis, predictionAnalysis: PredictionAnalysis) {
    this.codebaseAnalysis = codebaseAnalysis;
    this.predictionAnalysis = predictionAnalysis;
  }

  /**
   * 生成代码
   */
  async generateCode(request: CodeGenerationRequest): Promise<CodeGenerationResult> {
    console.log(`🚀 Starting code generation: ${request.type} - ${request.description}`);

    const startTime = Date.now();

    try {
      // 1. 分析生成上下文
      const context = await this.analyzeGenerationContext(request);

      // 2. 选择生成策略
      const strategy = this.selectGenerationStrategy(request, context);

      // 3. 生成代码
      const generatedCode = await this.executeGeneration(request, context, strategy);

      // 4. 质量评估
      const qualityAssessment = await this.assessQuality(generatedCode, request);

      // 5. 生成建议
      const suggestions = await this.generateSuggestions(generatedCode, qualityAssessment, request);

      // 6. 计算统计信息
      const statistics = this.calculateStatistics(generatedCode, startTime);

      // 7. 生成后续步骤
      const nextSteps = this.generateNextSteps(generatedCode, qualityAssessment, request);

      console.log(`✅ Code generation completed in ${statistics.generationTime}ms`);

      return {
        generatedCode,
        qualityAssessment,
        suggestions,
        statistics,
        nextSteps
      };

    } catch (error) {
      console.error(`❌ Code generation failed:`, error);
      throw new Error(`Code generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分析生成上下文
   */
  private async analyzeGenerationContext(request: CodeGenerationRequest) {
    console.log(`🔍 Analyzing generation context...`);

    // 提取项目上下文
    const projectContext = await extractProjectContext(
      this.codebaseAnalysis,
      request.context.projectRoot
    );

    // 分析相关文件
    const relatedFiles = this.findRelatedFiles(request);

    // 提取架构模式
    const architecturePatterns = this.extractArchitecturePatterns();

    // 分析依赖关系
    const dependencies = this.analyzeDependencies(request);

    return {
      ...request.context,
      projectContext,
      relatedFiles,
      architecturePatterns,
      dependencies
    };
  }

  /**
   * 选择生成策略
   */
  private selectGenerationStrategy(request: CodeGenerationRequest, context: any) {
    console.log(`🎯 Selecting generation strategy for ${request.type}...`);

    const strategies = {
      function: this.generateFunction.bind(this),
      class: this.generateClass.bind(this),
      module: this.generateModule.bind(this),
      component: this.generateComponent.bind(this),
      test: this.generateTest.bind(this),
      refactor: this.generateRefactor.bind(this),
      optimization: this.generateOptimization.bind(this)
    };

    return strategies[request.type] || strategies.function;
  }

  /**
   * 执行代码生成
   */
  private async executeGeneration(
    request: CodeGenerationRequest,
    context: any,
    strategy: Function
  ): Promise<GeneratedCode[]> {
    console.log(`⚡ Executing generation strategy...`);

    return await strategy(request, context);
  }

  /**
   * 生成函数
   */
  private async generateFunction(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🔧 Generating function: ${request.description}`);

    // 获取函数模板
    const template = await getCodeTemplate("function", request.language);

    // 分析函数需求
    const functionSpec = this.analyzeFunctionRequirements(request, context);

    // 渲染模板
    const code = await renderTemplate(template, functionSpec);

    // 生成测试（如果需要）
    const testCode = request.options.includeTests
      ? await this.generateFunctionTest(functionSpec, request.language)
      : null;

    const result: GeneratedCode[] = [{
      filePath: context.targetFile || `${functionSpec.name}.${this.getFileExtension(request.language)}`,
      content: code,
      type: "function",
      changeType: "create",
      dependencies: functionSpec.dependencies
    }];

    if (testCode) {
      result.push({
        filePath: `${functionSpec.name}.test.${this.getFileExtension(request.language)}`,
        content: testCode,
        type: "test",
        changeType: "create",
        dependencies: [...functionSpec.dependencies, "test-framework"]
      });
    }

    return result;
  }

  /**
   * 生成类
   */
  private async generateClass(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🏗️ Generating class: ${request.description}`);

    // 获取类模板
    const template = await getCodeTemplate("class", request.language);

    // 分析类需求
    const classSpec = this.analyzeClassRequirements(request, context);

    // 渲染模板
    const code = await renderTemplate(template, classSpec);

    return [{
      filePath: context.targetFile || `${classSpec.name}.${this.getFileExtension(request.language)}`,
      content: code,
      type: "class",
      changeType: "create",
      dependencies: classSpec.dependencies
    }];
  }

  /**
   * 生成模块
   */
  private async generateModule(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`📦 Generating module: ${request.description}`);

    // 分析模块需求
    const moduleSpec = this.analyzeModuleRequirements(request, context);

    const generatedFiles: GeneratedCode[] = [];

    // 生成主模块文件
    const mainTemplate = await getCodeTemplate("module", request.language);
    const mainCode = await renderTemplate(mainTemplate, moduleSpec);

    generatedFiles.push({
      filePath: `${moduleSpec.name}/index.${this.getFileExtension(request.language)}`,
      content: mainCode,
      type: "module",
      changeType: "create",
      dependencies: moduleSpec.dependencies
    });

    // 生成子组件
    for (const component of moduleSpec.components) {
      const componentCode = await this.generateComponent({
        ...request,
        description: component.description,
        type: "component"
      }, { ...context, targetFile: `${moduleSpec.name}/${component.name}.${this.getFileExtension(request.language)}` });

      generatedFiles.push(...componentCode);
    }

    return generatedFiles;
  }

  /**
   * 生成组件
   */
  private async generateComponent(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🧩 Generating component: ${request.description}`);

    // 根据语言选择组件类型
    const componentType = this.determineComponentType(request.language);
    const template = await getCodeTemplate(componentType, request.language);

    // 分析组件需求
    const componentSpec = this.analyzeComponentRequirements(request, context);

    // 渲染模板
    const code = await renderTemplate(template, componentSpec);

    return [{
      filePath: context.targetFile || `${componentSpec.name}.${this.getFileExtension(request.language)}`,
      content: code,
      type: "component",
      changeType: "create",
      dependencies: componentSpec.dependencies
    }];
  }

  /**
   * 生成测试
   */
  private async generateTest(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🧪 Generating intelligent tests: ${request.description}`);

    // 使用智能测试生成器
    const testGenerator = new IntelligentTestGenerator(
      this.codebaseAnalysis,
      this.predictionAnalysis
    );

    // 构建测试生成配置
    const testConfig = this.buildTestGenerationConfig(request, context);

    // 生成测试套件
    const testSuite = await testGenerator.generateTestSuite(
      context.targetFile || request.context.targetFile,
      testConfig
    );

    // 使用框架适配器生成代码
    const adapter = TestFrameworkAdapterFactory.getAdapter(testConfig.framework);
    const testCode = adapter.generateTestCode(testSuite);

    // 评估测试质量
    const qualityAssessor = new TestQualityAssessor(this.codebaseAnalysis);
    const qualityAssessment = await qualityAssessor.assessTestQuality(testSuite);

    // 生成测试文件
    const testFilePath = this.generateTestFilePath(context.targetFile || request.context.targetFile, testConfig.framework);

    const generatedCode: GeneratedCode[] = [{
      filePath: testFilePath,
      content: this.formatTestSuiteWithQuality(testCode, testSuite, qualityAssessment),
      type: "test",
      changeType: "create",
      dependencies: []
    }];

    return generatedCode;
  }

  /**
   * 构建测试生成配置
   */
  private buildTestGenerationConfig(request: CodeGenerationRequest, context: any): any {
    return {
      testTypes: request.options.includeTests ? ["unit", "integration"] : ["unit"],
      framework: this.detectTestFramework(context.projectRoot) || "jest",
      coverageTarget: 80,
      mockingStrategy: "standard" as const,
      includeE2E: false
    };
  }

  /**
   * 检测项目使用的测试框架
   */
  private detectTestFramework(projectRoot: string): string {
    // 简化实现：检查 package.json 中的依赖
    try {
      const fs = require('fs');
      const path = require('path');
      const packageJsonPath = path.join(projectRoot, 'package.json');

      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

        if (deps.vitest) return "vitest";
        if (deps.jest) return "jest";
        if (deps.mocha) return "mocha";
        if (deps.cypress) return "cypress";
        if (deps['@playwright/test']) return "playwright";
      }
    } catch (error) {
      console.warn('Failed to detect test framework:', error);
    }

    return "jest"; // 默认
  }

  /**
   * 生成测试文件路径
   */
  private generateTestFilePath(targetFile: string, framework: string): string {
    const path = require('path');
    const ext = path.extname(targetFile);
    const baseName = path.basename(targetFile, ext);
    const dir = path.dirname(targetFile);

    // 根据框架选择测试文件命名约定
    if (framework === "cypress" || framework === "playwright") {
      return path.join(dir, `${baseName}.e2e${ext}`);
    }

    return path.join(dir, `${baseName}.test${ext}`);
  }

  /**
   * 格式化测试套件和质量报告
   */
  private formatTestSuiteWithQuality(testCode: string, testSuite: any, qualityAssessment: any): string {
    return `${testCode}

/*
=== 测试质量报告 ===
总体评分: ${qualityAssessment.overallScore}/100 (${qualityAssessment.grade})
测试用例数量: ${testSuite.testCases.length}
覆盖率目标: ${testSuite.coverageTarget}%

质量维度:
- 完整性: ${Math.round(qualityAssessment.dimensions.completeness * 100)}%
- 正确性: ${Math.round(qualityAssessment.dimensions.correctness * 100)}%
- 可维护性: ${Math.round(qualityAssessment.dimensions.maintainability * 100)}%
- 可读性: ${Math.round(qualityAssessment.dimensions.readability * 100)}%
- 有效性: ${Math.round(qualityAssessment.dimensions.effectiveness * 100)}%

${qualityAssessment.improvements.length > 0 ? `
改进建议:
${qualityAssessment.improvements.map((imp: string) => `- ${imp}`).join('\n')}
` : ''}
*/`;
  }

  /**
   * 生成重构建议
   */
  private async generateRefactor(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`🔄 Generating intelligent refactor suggestions: ${request.description}`);

    // 使用高级重构分析器生成智能建议
    const advancedAnalyzer = new AdvancedRefactorAnalyzer(
      this.codebaseAnalysis,
      this.predictionAnalysis
    );

    const refactorSuggestions = await advancedAnalyzer.generateIntelligentRefactorSuggestions(
      context.targetFile
    );

    const generatedCode: GeneratedCode[] = [];

    // 为每个重构建议执行安全检查
    const safetyChecker = new RefactorSafetyChecker(
      this.codebaseAnalysis,
      context.projectRoot
    );

    for (const suggestion of refactorSuggestions) {
      // 执行安全检查
      const safetyCheck = await safetyChecker.performSafetyCheck(suggestion);

      // 格式化重构建议（包含安全检查结果）
      const content = this.formatAdvancedRefactorSuggestion(suggestion, safetyCheck);

      generatedCode.push({
        filePath: suggestion.targetFile,
        content,
        type: "refactor",
        changeType: "modify",
        dependencies: []
      });
    }

    return generatedCode;
  }

  /**
   * 格式化重构建议
   */
  private formatRefactorSuggestion(suggestion: any): string {
    return `# 重构建议: ${suggestion.description}

## 重构类型
${suggestion.type}

## 风险级别
${suggestion.risk}

## 重构前代码
\`\`\`typescript
${suggestion.beforeCode}
\`\`\`

## 重构后代码
\`\`\`typescript
${suggestion.afterCode}
\`\`\`

## 预期收益
${suggestion.benefits.map((benefit: string) => `- ${benefit}`).join('\n')}

## 影响分析
- 影响文件: ${suggestion.impact.affectedFiles.join(', ')}
- 破坏性变更: ${suggestion.impact.breakingChanges ? '是' : '否'}
- 测试影响: ${suggestion.impact.testImpact}
`;
  }

  /**
   * 格式化高级重构建议（包含安全检查）
   */
  private formatAdvancedRefactorSuggestion(suggestion: any, safetyCheck: any): string {
    return `# 🔄 智能重构建议: ${suggestion.description}

## 📋 重构信息
- **重构类型**: ${suggestion.type}
- **目标文件**: ${suggestion.targetFile}
- **风险级别**: ${suggestion.risk}

## 🔒 安全检查结果
- **安全状态**: ${safetyCheck.isSafe ? '✅ 安全' : '⚠️ 需要注意'}
- **整体风险**: ${safetyCheck.riskLevel}

### 检查项详情
${safetyCheck.checks.map((check: any) =>
      `- ${check.passed ? '✅' : '❌'} **${check.name}**: ${check.description}${check.recommendation ? `\n  💡 建议: ${check.recommendation}` : ''}`
    ).join('\n')}

## 📝 重构前代码
\`\`\`typescript
${suggestion.beforeCode}
\`\`\`

## ✨ 重构后代码
\`\`\`typescript
${suggestion.afterCode}
\`\`\`

## 🎯 预期收益
${suggestion.benefits.map((benefit: string) => `- ${benefit}`).join('\n')}

## 📊 影响分析
- **影响文件**: ${suggestion.impact.affectedFiles.join(', ')}
- **影响函数**: ${suggestion.impact.affectedFunctions.join(', ') || '无'}
- **影响类**: ${suggestion.impact.affectedClasses.join(', ') || '无'}
- **破坏性变更**: ${suggestion.impact.breakingChanges ? '是' : '否'}
- **测试影响**: ${suggestion.impact.testImpact}

## 🛡️ 预防措施
${safetyCheck.preventiveMeasures.map((measure: any) =>
      `- **${measure.type}**: ${measure.description}${measure.command ? `\n  \`${measure.command}\`` : ''}`
    ).join('\n')}

## 🔄 回滚计划
**预估回滚时间**: ${safetyCheck.rollbackPlan.estimatedTime}
**回滚风险**: ${safetyCheck.rollbackPlan.rollbackRisk}

### 回滚步骤
${safetyCheck.rollbackPlan.steps.map((step: any) =>
      `${step.order}. ${step.description}${step.command ? `\n   \`${step.command}\`` : ''}${step.verification ? `\n   验证: ${step.verification}` : ''}`
    ).join('\n')}

## ⚡ 执行建议
${safetyCheck.isSafe
        ? '✅ 此重构建议已通过安全检查，可以安全执行。建议按照预防措施进行操作。'
        : '⚠️ 此重构建议存在风险，请仔细评估后再执行。建议先在测试环境中验证。'
      }
`;
  }

  /**
   * 生成性能优化
   */
  private async generateOptimization(
    request: CodeGenerationRequest,
    context: any
  ): Promise<GeneratedCode[]> {
    console.log(`⚡ Generating intelligent performance optimizations: ${request.description}`);

    // 使用性能分析器分析瓶颈
    const performanceAnalyzer = new PerformanceAnalyzer(
      this.codebaseAnalysis,
      this.predictionAnalysis
    );

    const performanceAnalysis = await performanceAnalyzer.analyzeFilePerformance(
      context.targetFile || request.context.targetFile
    );

    // 使用优化生成器生成优化策略
    const optimizationGenerator = new OptimizationGenerator(this.codebaseAnalysis);

    const optimizationSuite = await optimizationGenerator.generateOptimizationSuite(
      performanceAnalysis,
      request.language
    );

    // 生成优化代码文件
    const generatedOptimizations: GeneratedCode[] = [];

    // 为每个优化生成代码文件
    for (const optimization of optimizationSuite.optimizations) {
      generatedOptimizations.push({
        filePath: this.generateOptimizationFilePath(context.targetFile, optimization.type),
        content: this.formatOptimizationCode(optimization, optimizationSuite),
        type: "optimization",
        changeType: "create",
        dependencies: []
      });
    }

    // 生成性能监控代码
    generatedOptimizations.push({
      filePath: this.generateMonitoringFilePath(context.targetFile),
      content: this.formatMonitoringCode(optimizationSuite),
      type: "monitoring",
      changeType: "create",
      dependencies: []
    });

    // 生成基准测试代码
    generatedOptimizations.push({
      filePath: this.generateBenchmarkFilePath(context.targetFile),
      content: optimizationSuite.performanceBenchmark.benchmarkCode,
      type: "benchmark",
      changeType: "create",
      dependencies: []
    });

    return generatedOptimizations;
  }

  /**
   * 生成优化文件路径
   */
  private generateOptimizationFilePath(targetFile: string, optimizationType: string): string {
    const path = require('path');
    const ext = path.extname(targetFile);
    const baseName = path.basename(targetFile, ext);
    const dir = path.dirname(targetFile);

    return path.join(dir, `${baseName}.${optimizationType}.optimized${ext}`);
  }

  /**
   * 格式化优化代码
   */
  private formatOptimizationCode(optimization: any, suite: any): string {
    return `// 🚀 性能优化: ${optimization.type}
// 目标瓶颈: ${optimization.targetBottleneck.description}
// 预期改进: 执行时间 ${optimization.expectedImprovement.executionTime}%, 内存使用 ${optimization.expectedImprovement.memoryUsage}%

${optimization.explanation}

// ============================================================================
// 优化前代码
// ============================================================================
/*
${optimization.beforeCode}
*/

// ============================================================================
// 优化后代码
// ============================================================================
${optimization.afterCode}

// ============================================================================
// 实施步骤
// ============================================================================
/*
${optimization.implementationSteps.map((step: string, index: number) => `${index + 1}. ${step}`).join('\n')}
*/

// ============================================================================
// 注意事项
// ============================================================================
/*
${optimization.considerations.map((consideration: string) => `- ${consideration}`).join('\n')}
*/

// ============================================================================
// 测试建议
// ============================================================================
/*
${optimization.testingRecommendations.map((rec: string) => `- ${rec}`).join('\n')}
*/`;
  }

  /**
   * 生成监控文件路径
   */
  private generateMonitoringFilePath(targetFile: string): string {
    const path = require('path');
    const dir = path.dirname(targetFile);
    return path.join(dir, 'performance-monitoring.js');
  }

  /**
   * 格式化监控代码
   */
  private formatMonitoringCode(suite: any): string {
    return `// 🔍 性能监控系统
// 自动生成的性能监控代码

const { performance } = require('perf_hooks');

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.alerts = new Map();
  }

  /**
   * 开始性能监控
   */
  startMonitoring(operationName) {
    const startTime = performance.now();
    const startMemory = process.memoryUsage();

    return {
      operationName,
      startTime,
      startMemory,
      end: () => this.endMonitoring(operationName, startTime, startMemory)
    };
  }

  /**
   * 结束性能监控
   */
  endMonitoring(operationName, startTime, startMemory) {
    const endTime = performance.now();
    const endMemory = process.memoryUsage();

    const metrics = {
      duration: endTime - startTime,
      memoryDelta: endMemory.heapUsed - startMemory.heapUsed,
      timestamp: new Date().toISOString()
    };

    this.recordMetrics(operationName, metrics);
    this.checkAlerts(operationName, metrics);

    return metrics;
  }

  /**
   * 记录性能指标
   */
  recordMetrics(operationName, metrics) {
    if (!this.metrics.has(operationName)) {
      this.metrics.set(operationName, []);
    }

    this.metrics.get(operationName).push(metrics);

    // 保留最近100条记录
    const records = this.metrics.get(operationName);
    if (records.length > 100) {
      records.splice(0, records.length - 100);
    }
  }

  /**
   * 检查告警阈值
   */
  checkAlerts(operationName, metrics) {
    const thresholds = {
${suite.monitoringRecommendations.map((rec: any) =>
      Object.entries(rec.alertThresholds).map(([metric, threshold]) =>
        `      '${metric}': ${threshold}`
      ).join(',\n')
    ).join(',\n')}
    };

    for (const [metric, threshold] of Object.entries(thresholds)) {
      if (metrics[metric] && metrics[metric] > threshold) {
        console.warn(\`⚠️ Performance Alert: \${operationName} \${metric} exceeded threshold (\${metrics[metric]} > \${threshold})\`);
      }
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(operationName) {
    const records = this.metrics.get(operationName) || [];
    if (records.length === 0) return null;

    const durations = records.map(r => r.duration);
    const memoryDeltas = records.map(r => r.memoryDelta);

    return {
      operationName,
      totalRecords: records.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      maxDuration: Math.max(...durations),
      minDuration: Math.min(...durations),
      averageMemoryDelta: memoryDeltas.reduce((a, b) => a + b, 0) / memoryDeltas.length,
      lastUpdated: records[records.length - 1].timestamp
    };
  }
}

// 导出监控实例
module.exports = new PerformanceMonitor();

// 使用示例:
// const monitor = require('./performance-monitoring');
// const operation = monitor.startMonitoring('dataProcessing');
// // ... 执行操作
// const metrics = operation.end();
// console.log('Performance metrics:', metrics);`;
  }

  /**
   * 生成基准测试文件路径
   */
  private generateBenchmarkFilePath(targetFile: string): string {
    const path = require('path');
    const dir = path.dirname(targetFile);
    return path.join(dir, 'performance-benchmark.js');
  }

  // ============================================================================
  // 辅助方法
  // ============================================================================

  /**
   * 查找相关文件
   */
  private findRelatedFiles(request: CodeGenerationRequest): FileAnalysis[] {
    // 基于描述和上下文查找相关文件
    return this.codebaseAnalysis.files.filter(file =>
      this.isFileRelevant(file, request)
    );
  }

  /**
   * 判断文件是否相关
   */
  private isFileRelevant(file: FileAnalysis, request: CodeGenerationRequest): boolean {
    // 简单的相关性判断逻辑
    const keywords = request.description.toLowerCase().split(' ');
    const fileName = file.path.toLowerCase();

    return keywords.some(keyword => fileName.includes(keyword));
  }

  /**
   * 提取架构模式
   */
  private extractArchitecturePatterns(): string[] {
    // 从代码库分析中提取架构模式
    return this.codebaseAnalysis.architecture?.patterns || [];
  }

  /**
   * 分析依赖关系
   */
  private analyzeDependencies(request: CodeGenerationRequest) {
    // 分析项目依赖
    return this.codebaseAnalysis.dependencies || [];
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(language: ProgrammingLanguage): string {
    const extensions = {
      typescript: "ts",
      javascript: "js",
      python: "py",
      java: "java",
      go: "go",
      rust: "rs"
    };

    return extensions[language] || "txt";
  }

  /**
   * 分析函数需求
   */
  private analyzeFunctionRequirements(request: CodeGenerationRequest, context: any) {
    // 使用 NLP 处理从描述中提取函数规格
    const codeStructure = parseCodeDescription(request.description, request.type, request.language);

    return {
      name: codeStructure.name,
      parameters: codeStructure.parameters,
      returnType: codeStructure.returnType || "void",
      dependencies: codeStructure.dependencies,
      documentation: request.options.includeComments,
      isAsync: codeStructure.isAsync,
      complexity: codeStructure.complexity
    };
  }

  /**
   * 分析类需求
   */
  private analyzeClassRequirements(request: CodeGenerationRequest, context: any) {
    const codeStructure = parseCodeDescription(request.description, request.type, request.language);

    return {
      name: codeStructure.name,
      properties: codeStructure.properties,
      methods: codeStructure.methods,
      inheritance: null, // 暂时简化
      dependencies: codeStructure.dependencies,
      hasConstructor: codeStructure.properties.length > 0,
      constructorParams: codeStructure.properties.map(prop => ({
        name: prop.name,
        type: prop.type,
        description: prop.description
      }))
    };
  }

  /**
   * 分析模块需求
   */
  private analyzeModuleRequirements(request: CodeGenerationRequest, context: any) {
    return {
      name: this.extractModuleName(request.description),
      components: this.extractComponents(request.description),
      exports: this.extractExports(request.description),
      dependencies: this.extractDependencies(request.description, context)
    };
  }

  /**
   * 分析组件需求
   */
  private analyzeComponentRequirements(request: CodeGenerationRequest, context: any) {
    return {
      name: this.extractComponentName(request.description),
      props: this.extractProps(request.description),
      state: this.extractState(request.description),
      methods: this.extractMethods(request.description),
      dependencies: this.extractDependencies(request.description, context)
    };
  }

  // 这些提取方法需要实现具体的NLP逻辑
  private extractFunctionName(description: string): string {
    // 简单实现，实际需要更复杂的NLP
    const match = description.match(/function\s+(\w+)/i) || description.match(/(\w+)\s+function/i);
    return match ? match[1] : "generatedFunction";
  }

  private extractParameters(description: string): any[] {
    // 简单实现
    return [];
  }

  private extractReturnType(description: string): string {
    // 简单实现
    return "any";
  }

  private extractDependencies(description: string, context: any): string[] {
    // 简单实现
    return [];
  }

  private extractClassName(description: string): string {
    const match = description.match(/class\s+(\w+)/i) || description.match(/(\w+)\s+class/i);
    return match ? match[1] : "GeneratedClass";
  }

  private extractProperties(description: string): any[] {
    return [];
  }

  private extractMethods(description: string): any[] {
    return [];
  }

  private extractInheritance(description: string, context: any): string | null {
    return null;
  }

  private extractModuleName(description: string): string {
    const match = description.match(/module\s+(\w+)/i) || description.match(/(\w+)\s+module/i);
    return match ? match[1] : "generatedModule";
  }

  private extractComponents(description: string): any[] {
    return [];
  }

  private extractExports(description: string): any[] {
    return [];
  }

  private extractComponentName(description: string): string {
    const match = description.match(/component\s+(\w+)/i) || description.match(/(\w+)\s+component/i);
    return match ? match[1] : "GeneratedComponent";
  }

  private extractProps(description: string): any[] {
    return [];
  }

  private extractState(description: string): any[] {
    return [];
  }

  private determineComponentType(language: ProgrammingLanguage): string {
    if (language === "typescript" || language === "javascript") {
      return "react-component"; // 或 "vue-component"
    }
    return "component";
  }

  private async generateFunctionTest(functionSpec: any, language: ProgrammingLanguage): Promise<string> {
    const template = await getCodeTemplate("function-test", language);
    return await renderTemplate(template, functionSpec);
  }

  private analyzeTestTarget(request: CodeGenerationRequest, context: any) {
    return {
      targetFile: context.targetFile,
      needsUnitTests: true,
      needsIntegrationTests: false,
      testFramework: "jest" // 默认
    };
  }

  private async generateUnitTests(testTarget: any, language: ProgrammingLanguage): Promise<GeneratedCode[]> {
    // 实现单元测试生成
    return [];
  }

  private async generateIntegrationTests(testTarget: any, language: ProgrammingLanguage): Promise<GeneratedCode[]> {
    // 实现集成测试生成
    return [];
  }

  private identifyRefactorOpportunities(context: any): any[] {
    // 基于预测分析识别重构机会
    return [];
  }

  private async applyRefactoring(opportunity: any, language: ProgrammingLanguage): Promise<GeneratedCode> {
    // 应用重构
    return {
      filePath: opportunity.filePath,
      content: opportunity.refactoredCode,
      type: "refactor",
      changeType: "modify",
      dependencies: []
    };
  }

  private identifyOptimizationOpportunities(context: any): any[] {
    // 基于性能预测识别优化机会
    return [];
  }

  private async applyOptimization(opportunity: any, language: ProgrammingLanguage): Promise<GeneratedCode> {
    // 应用性能优化
    return {
      filePath: opportunity.filePath,
      content: opportunity.optimizedCode,
      type: "optimization",
      changeType: "modify",
      dependencies: []
    };
  }

  private async assessQuality(generatedCode: GeneratedCode[], request: CodeGenerationRequest): Promise<QualityAssessment> {
    return await assessGeneratedCodeQuality(generatedCode, this.codebaseAnalysis);
  }

  private async generateSuggestions(
    generatedCode: GeneratedCode[],
    qualityAssessment: QualityAssessment,
    request: CodeGenerationRequest
  ): Promise<GenerationSuggestion[]> {
    const suggestions: GenerationSuggestion[] = [];

    // 基于质量评估生成建议
    if (qualityAssessment.overallScore < 0.8) {
      suggestions.push({
        type: "improvement",
        message: "生成的代码质量可以进一步改进",
        affectedFiles: generatedCode.map(code => code.filePath),
        severity: "medium",
        fixSuggestion: "考虑重构以提高代码质量"
      });
    }

    return suggestions;
  }

  private calculateStatistics(generatedCode: GeneratedCode[], startTime: number): GenerationStatistics {
    const endTime = Date.now();
    const totalLines = generatedCode.reduce((sum, code) =>
      sum + code.content.split('\n').length, 0
    );

    return {
      filesGenerated: generatedCode.length,
      linesGenerated: totalLines,
      generationTime: endTime - startTime,
      templatesUsed: [], // 需要跟踪使用的模板
      patternsApplied: [] // 需要跟踪应用的模式
    };
  }

  private generateNextSteps(
    generatedCode: GeneratedCode[],
    qualityAssessment: QualityAssessment,
    request: CodeGenerationRequest
  ): NextStep[] {
    const nextSteps: NextStep[] = [];

    // 总是建议测试
    nextSteps.push({
      description: "运行生成的测试以验证代码功能",
      type: "test",
      priority: "high",
      estimatedTime: "5-10 分钟",
      relatedTools: ["generate-tests"]
    });

    // 如果质量不够高，建议审查
    if (qualityAssessment.overallScore < 0.8) {
      nextSteps.push({
        description: "审查生成的代码并进行必要的改进",
        type: "review",
        priority: "medium",
        estimatedTime: "15-30 分钟",
        relatedTools: ["suggest-refactor"]
      });
    }

    return nextSteps;
  }
}
