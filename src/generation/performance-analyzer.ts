/**
 * 智能代码生成 - 性能分析引擎
 * 识别代码中的性能瓶颈并提供优化建议
 */

import { CodebaseAnalysis, FileAnalysis, FunctionInfo, ClassInfo } from "../analysis/types.js";
import { PredictionAnalysis } from "../prediction/types.js";

/**
 * 性能瓶颈类型
 */
export type PerformanceBottleneckType = 
  | "algorithm_complexity"
  | "memory_usage"
  | "io_operations"
  | "database_queries"
  | "network_requests"
  | "synchronous_blocking"
  | "resource_leaks"
  | "inefficient_loops";

/**
 * 性能瓶颈
 */
export interface PerformanceBottleneck {
  /** 瓶颈类型 */
  type: PerformanceBottleneckType;
  /** 严重程度 */
  severity: "low" | "medium" | "high" | "critical";
  /** 位置信息 */
  location: {
    file: string;
    function?: string;
    line?: number;
    column?: number;
  };
  /** 问题描述 */
  description: string;
  /** 当前性能指标 */
  currentMetrics: PerformanceMetrics;
  /** 预期改进 */
  expectedImprovement: PerformanceImprovement;
  /** 优化建议 */
  optimizationSuggestions: OptimizationSuggestion[];
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 时间复杂度 */
  timeComplexity: string;
  /** 空间复杂度 */
  spaceComplexity: string;
  /** 预估执行时间 (ms) */
  estimatedExecutionTime: number;
  /** 内存使用量 (MB) */
  memoryUsage: number;
  /** I/O 操作次数 */
  ioOperations: number;
}

/**
 * 性能改进预期
 */
export interface PerformanceImprovement {
  /** 时间复杂度改进 */
  timeComplexityImprovement: string;
  /** 空间复杂度改进 */
  spaceComplexityImprovement: string;
  /** 执行时间改进百分比 */
  executionTimeImprovement: number;
  /** 内存使用改进百分比 */
  memoryUsageImprovement: number;
  /** 整体性能提升 */
  overallImprovement: number;
}

/**
 * 优化建议
 */
export interface OptimizationSuggestion {
  /** 建议类型 */
  type: "algorithm" | "caching" | "async" | "batching" | "indexing" | "lazy_loading";
  /** 建议标题 */
  title: string;
  /** 详细描述 */
  description: string;
  /** 实现难度 */
  difficulty: "easy" | "medium" | "hard";
  /** 预期收益 */
  expectedBenefit: number;
  /** 代码示例 */
  codeExample?: string;
  /** 相关资源 */
  resources?: string[];
}

/**
 * 性能分析结果
 */
export interface PerformanceAnalysisResult {
  /** 分析的文件 */
  targetFile: string;
  /** 发现的瓶颈 */
  bottlenecks: PerformanceBottleneck[];
  /** 整体性能评分 (0-100) */
  overallScore: number;
  /** 性能等级 */
  performanceGrade: "A" | "B" | "C" | "D" | "F";
  /** 优化优先级 */
  optimizationPriorities: OptimizationPriority[];
  /** 性能报告 */
  report: PerformanceReport;
}

/**
 * 优化优先级
 */
export interface OptimizationPriority {
  /** 瓶颈引用 */
  bottleneck: PerformanceBottleneck;
  /** 优先级分数 */
  priorityScore: number;
  /** 优先级等级 */
  priority: "low" | "medium" | "high" | "critical";
  /** 推荐执行顺序 */
  executionOrder: number;
}

/**
 * 性能报告
 */
export interface PerformanceReport {
  /** 性能统计 */
  statistics: PerformanceStatistics;
  /** 关键发现 */
  keyFindings: string[];
  /** 快速修复建议 */
  quickFixes: string[];
  /** 长期优化建议 */
  longTermOptimizations: string[];
}

/**
 * 性能统计
 */
export interface PerformanceStatistics {
  /** 分析的函数数量 */
  functionsAnalyzed: number;
  /** 发现的瓶颈数量 */
  bottlenecksFound: number;
  /** 高优先级瓶颈数量 */
  highPriorityBottlenecks: number;
  /** 平均复杂度 */
  averageComplexity: number;
  /** 预估总体改进潜力 */
  totalImprovementPotential: number;
}

/**
 * 性能分析引擎
 */
export class PerformanceAnalyzer {
  private codebaseAnalysis: CodebaseAnalysis;
  private predictionAnalysis: PredictionAnalysis;

  constructor(codebaseAnalysis: CodebaseAnalysis, predictionAnalysis: PredictionAnalysis) {
    this.codebaseAnalysis = codebaseAnalysis;
    this.predictionAnalysis = predictionAnalysis;
  }

  /**
   * 分析文件性能
   */
  async analyzeFilePerformance(targetFile: string): Promise<PerformanceAnalysisResult> {
    console.log(`⚡ Analyzing performance for ${targetFile}...`);
    
    const file = this.findFile(targetFile);
    if (!file) {
      throw new Error(`File ${targetFile} not found in codebase analysis`);
    }
    
    // 1. 识别性能瓶颈
    const bottlenecks = await this.identifyBottlenecks(file);
    
    // 2. 计算性能评分
    const overallScore = this.calculatePerformanceScore(bottlenecks);
    
    // 3. 确定性能等级
    const performanceGrade = this.calculatePerformanceGrade(overallScore);
    
    // 4. 生成优化优先级
    const optimizationPriorities = this.generateOptimizationPriorities(bottlenecks);
    
    // 5. 生成性能报告
    const report = this.generatePerformanceReport(file, bottlenecks, optimizationPriorities);
    
    console.log(`✅ Performance analysis complete - Grade: ${performanceGrade}, Score: ${overallScore}`);
    
    return {
      targetFile,
      bottlenecks,
      overallScore,
      performanceGrade,
      optimizationPriorities,
      report
    };
  }

  /**
   * 识别性能瓶颈
   */
  private async identifyBottlenecks(file: FileAnalysis): Promise<PerformanceBottleneck[]> {
    const bottlenecks: PerformanceBottleneck[] = [];
    
    // 分析函数性能
    if (file.functions) {
      for (const func of file.functions) {
        bottlenecks.push(...await this.analyzeFunctionPerformance(func, file.path));
      }
    }
    
    // 分析类性能
    if (file.classes) {
      for (const cls of file.classes) {
        bottlenecks.push(...await this.analyzeClassPerformance(cls, file.path));
      }
    }
    
    // 基于预测分析识别潜在瓶颈
    bottlenecks.push(...await this.identifyPredictedBottlenecks(file));
    
    return bottlenecks;
  }

  /**
   * 分析函数性能
   */
  private async analyzeFunctionPerformance(func: FunctionInfo, filePath: string): Promise<PerformanceBottleneck[]> {
    const bottlenecks: PerformanceBottleneck[] = [];
    
    // 1. 算法复杂度分析
    if (func.complexity > 15) {
      bottlenecks.push({
        type: "algorithm_complexity",
        severity: func.complexity > 25 ? "critical" : "high",
        location: { file: filePath, function: func.name },
        description: `函数 ${func.name} 的圈复杂度过高 (${func.complexity})`,
        currentMetrics: {
          timeComplexity: this.estimateTimeComplexity(func),
          spaceComplexity: "O(1)",
          estimatedExecutionTime: func.complexity * 10,
          memoryUsage: func.complexity * 0.5,
          ioOperations: 0
        },
        expectedImprovement: {
          timeComplexityImprovement: "O(n) → O(log n)",
          spaceComplexityImprovement: "无变化",
          executionTimeImprovement: 60,
          memoryUsageImprovement: 30,
          overallImprovement: 50
        },
        optimizationSuggestions: [
          {
            type: "algorithm",
            title: "重构复杂函数",
            description: "将复杂函数拆分为多个小函数，降低圈复杂度",
            difficulty: "medium",
            expectedBenefit: 50,
            codeExample: `// 重构前
function complexFunction(data) {
  // 50+ 行复杂逻辑
}

// 重构后
function processData(data) {
  const validated = validateData(data);
  const processed = transformData(validated);
  return formatResult(processed);
}`
          }
        ]
      });
    }
    
    // 2. 循环效率分析
    if (this.hasNestedLoops(func)) {
      bottlenecks.push({
        type: "inefficient_loops",
        severity: "medium",
        location: { file: filePath, function: func.name },
        description: `函数 ${func.name} 包含嵌套循环，可能导致性能问题`,
        currentMetrics: {
          timeComplexity: "O(n²)",
          spaceComplexity: "O(1)",
          estimatedExecutionTime: 1000,
          memoryUsage: 2,
          ioOperations: 0
        },
        expectedImprovement: {
          timeComplexityImprovement: "O(n²) → O(n)",
          spaceComplexityImprovement: "O(1) → O(n)",
          executionTimeImprovement: 80,
          memoryUsageImprovement: -50,
          overallImprovement: 65
        },
        optimizationSuggestions: [
          {
            type: "algorithm",
            title: "优化循环结构",
            description: "使用 Map 或 Set 数据结构替代嵌套循环",
            difficulty: "medium",
            expectedBenefit: 65,
            codeExample: `// 优化前
for (let i = 0; i < arr1.length; i++) {
  for (let j = 0; j < arr2.length; j++) {
    if (arr1[i] === arr2[j]) {
      // 处理匹配
    }
  }
}

// 优化后
const set2 = new Set(arr2);
for (let item of arr1) {
  if (set2.has(item)) {
    // 处理匹配
  }
}`
          }
        ]
      });
    }
    
    // 3. 同步阻塞操作分析
    if (this.hasSynchronousBlocking(func)) {
      bottlenecks.push({
        type: "synchronous_blocking",
        severity: "high",
        location: { file: filePath, function: func.name },
        description: `函数 ${func.name} 包含同步阻塞操作`,
        currentMetrics: {
          timeComplexity: "O(1)",
          spaceComplexity: "O(1)",
          estimatedExecutionTime: 5000,
          memoryUsage: 1,
          ioOperations: 10
        },
        expectedImprovement: {
          timeComplexityImprovement: "无变化",
          spaceComplexityImprovement: "无变化",
          executionTimeImprovement: 90,
          memoryUsageImprovement: 0,
          overallImprovement: 85
        },
        optimizationSuggestions: [
          {
            type: "async",
            title: "异步化处理",
            description: "将同步操作改为异步操作，避免阻塞",
            difficulty: "easy",
            expectedBenefit: 85,
            codeExample: `// 同步版本
function processData(data) {
  const result = fs.readFileSync('data.json');
  return JSON.parse(result);
}

// 异步版本
async function processData(data) {
  const result = await fs.promises.readFile('data.json');
  return JSON.parse(result);
}`
          }
        ]
      });
    }
    
    return bottlenecks;
  }

  /**
   * 分析类性能
   */
  private async analyzeClassPerformance(cls: ClassInfo, filePath: string): Promise<PerformanceBottleneck[]> {
    const bottlenecks: PerformanceBottleneck[] = [];
    
    // 分析类的方法数量
    if (cls.methods && cls.methods.length > 20) {
      bottlenecks.push({
        type: "memory_usage",
        severity: "medium",
        location: { file: filePath, function: cls.name },
        description: `类 ${cls.name} 方法过多 (${cls.methods.length})，可能导致内存占用过高`,
        currentMetrics: {
          timeComplexity: "O(1)",
          spaceComplexity: "O(n)",
          estimatedExecutionTime: 100,
          memoryUsage: cls.methods.length * 0.1,
          ioOperations: 0
        },
        expectedImprovement: {
          timeComplexityImprovement: "无变化",
          spaceComplexityImprovement: "O(n) → O(log n)",
          executionTimeImprovement: 20,
          memoryUsageImprovement: 40,
          overallImprovement: 30
        },
        optimizationSuggestions: [
          {
            type: "lazy_loading",
            title: "类拆分和懒加载",
            description: "将大类拆分为多个小类，使用懒加载模式",
            difficulty: "hard",
            expectedBenefit: 30
          }
        ]
      });
    }
    
    return bottlenecks;
  }

  /**
   * 基于预测分析识别潜在瓶颈
   */
  private async identifyPredictedBottlenecks(file: FileAnalysis): Promise<PerformanceBottleneck[]> {
    const bottlenecks: PerformanceBottleneck[] = [];
    
    // 查找与性能相关的预测问题
    const performanceIssues = this.predictionAnalysis.issues.filter(issue => 
      issue.type === 'performance_bottleneck' && issue.location?.file === file.path
    );
    
    for (const issue of performanceIssues) {
      bottlenecks.push({
        type: "algorithm_complexity",
        severity: issue.severity as any,
        location: {
          file: file.path,
          function: issue.location?.function,
          line: issue.location?.line
        },
        description: `预测的性能问题: ${issue.description}`,
        currentMetrics: {
          timeComplexity: "O(n)",
          spaceComplexity: "O(1)",
          estimatedExecutionTime: 500,
          memoryUsage: 1,
          ioOperations: 0
        },
        expectedImprovement: {
          timeComplexityImprovement: "基于预测分析的优化",
          spaceComplexityImprovement: "无变化",
          executionTimeImprovement: 70,
          memoryUsageImprovement: 20,
          overallImprovement: 60
        },
        optimizationSuggestions: [
          {
            type: "algorithm",
            title: "预测驱动的优化",
            description: issue.suggestion || "基于预测分析的性能优化建议",
            difficulty: "medium",
            expectedBenefit: 60
          }
        ]
      });
    }
    
    return bottlenecks;
  }

  /**
   * 计算性能评分
   */
  private calculatePerformanceScore(bottlenecks: PerformanceBottleneck[]): number {
    let score = 100;
    
    for (const bottleneck of bottlenecks) {
      switch (bottleneck.severity) {
        case "critical":
          score -= 25;
          break;
        case "high":
          score -= 15;
          break;
        case "medium":
          score -= 10;
          break;
        case "low":
          score -= 5;
          break;
      }
    }
    
    return Math.max(0, score);
  }

  /**
   * 计算性能等级
   */
  private calculatePerformanceGrade(score: number): "A" | "B" | "C" | "D" | "F" {
    if (score >= 90) return "A";
    if (score >= 80) return "B";
    if (score >= 70) return "C";
    if (score >= 60) return "D";
    return "F";
  }

  /**
   * 生成优化优先级
   */
  private generateOptimizationPriorities(bottlenecks: PerformanceBottleneck[]): OptimizationPriority[] {
    return bottlenecks
      .map((bottleneck, index) => ({
        bottleneck,
        priorityScore: this.calculatePriorityScore(bottleneck),
        priority: this.determinePriority(bottleneck),
        executionOrder: index + 1
      }))
      .sort((a, b) => b.priorityScore - a.priorityScore)
      .map((priority, index) => ({ ...priority, executionOrder: index + 1 }));
  }

  /**
   * 计算优先级分数
   */
  private calculatePriorityScore(bottleneck: PerformanceBottleneck): number {
    let score = 0;
    
    // 基于严重程度
    switch (bottleneck.severity) {
      case "critical": score += 40; break;
      case "high": score += 30; break;
      case "medium": score += 20; break;
      case "low": score += 10; break;
    }
    
    // 基于预期改进
    score += bottleneck.expectedImprovement.overallImprovement * 0.5;
    
    // 基于实现难度（难度越低，优先级越高）
    const avgDifficulty = bottleneck.optimizationSuggestions.reduce((sum, suggestion) => {
      const difficultyScore = suggestion.difficulty === "easy" ? 3 : 
                             suggestion.difficulty === "medium" ? 2 : 1;
      return sum + difficultyScore;
    }, 0) / bottleneck.optimizationSuggestions.length;
    
    score += avgDifficulty * 10;
    
    return score;
  }

  /**
   * 确定优先级等级
   */
  private determinePriority(bottleneck: PerformanceBottleneck): "low" | "medium" | "high" | "critical" {
    if (bottleneck.severity === "critical") return "critical";
    if (bottleneck.expectedImprovement.overallImprovement > 70) return "high";
    if (bottleneck.expectedImprovement.overallImprovement > 40) return "medium";
    return "low";
  }

  /**
   * 生成性能报告
   */
  private generatePerformanceReport(
    file: FileAnalysis,
    bottlenecks: PerformanceBottleneck[],
    priorities: OptimizationPriority[]
  ): PerformanceReport {
    const statistics: PerformanceStatistics = {
      functionsAnalyzed: file.functions?.length || 0,
      bottlenecksFound: bottlenecks.length,
      highPriorityBottlenecks: bottlenecks.filter(b => b.severity === "high" || b.severity === "critical").length,
      averageComplexity: this.calculateAverageComplexity(file),
      totalImprovementPotential: bottlenecks.reduce((sum, b) => sum + b.expectedImprovement.overallImprovement, 0)
    };
    
    const keyFindings = this.generateKeyFindings(bottlenecks);
    const quickFixes = this.generateQuickFixes(priorities);
    const longTermOptimizations = this.generateLongTermOptimizations(priorities);
    
    return {
      statistics,
      keyFindings,
      quickFixes,
      longTermOptimizations
    };
  }

  // ============================================================================
  // 辅助方法
  // ============================================================================

  private findFile(filePath: string): FileAnalysis | undefined {
    return this.codebaseAnalysis.files.find(f => f.path === filePath);
  }

  private estimateTimeComplexity(func: FunctionInfo): string {
    if (func.complexity > 20) return "O(n²)";
    if (func.complexity > 10) return "O(n log n)";
    if (func.complexity > 5) return "O(n)";
    return "O(1)";
  }

  private hasNestedLoops(func: FunctionInfo): boolean {
    // 简化实现：基于复杂度估算
    return func.complexity > 15;
  }

  private hasSynchronousBlocking(func: FunctionInfo): boolean {
    // 简化实现：检查函数名是否包含同步操作关键词
    return func.name.includes('Sync') || func.name.includes('sync');
  }

  private calculateAverageComplexity(file: FileAnalysis): number {
    if (!file.functions || file.functions.length === 0) return 0;
    const totalComplexity = file.functions.reduce((sum, func) => sum + func.complexity, 0);
    return totalComplexity / file.functions.length;
  }

  private generateKeyFindings(bottlenecks: PerformanceBottleneck[]): string[] {
    const findings: string[] = [];
    
    const criticalBottlenecks = bottlenecks.filter(b => b.severity === "critical");
    if (criticalBottlenecks.length > 0) {
      findings.push(`发现 ${criticalBottlenecks.length} 个严重性能瓶颈需要立即处理`);
    }
    
    const algorithmIssues = bottlenecks.filter(b => b.type === "algorithm_complexity");
    if (algorithmIssues.length > 0) {
      findings.push(`${algorithmIssues.length} 个函数存在算法复杂度问题`);
    }
    
    const syncIssues = bottlenecks.filter(b => b.type === "synchronous_blocking");
    if (syncIssues.length > 0) {
      findings.push(`${syncIssues.length} 个函数包含同步阻塞操作`);
    }
    
    return findings;
  }

  private generateQuickFixes(priorities: OptimizationPriority[]): string[] {
    return priorities
      .filter(p => p.priority === "high" || p.priority === "critical")
      .slice(0, 3)
      .map(p => p.bottleneck.optimizationSuggestions[0]?.title || "优化建议")
      .filter(Boolean);
  }

  private generateLongTermOptimizations(priorities: OptimizationPriority[]): string[] {
    return priorities
      .filter(p => p.bottleneck.optimizationSuggestions.some(s => s.difficulty === "hard"))
      .slice(0, 3)
      .map(p => p.bottleneck.optimizationSuggestions.find(s => s.difficulty === "hard")?.title || "长期优化")
      .filter(Boolean);
  }
}
