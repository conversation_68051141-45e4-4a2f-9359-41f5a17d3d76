/**
 * 智能代码生成 - 测试框架适配器
 * 支持多种主流测试框架的代码生成
 */

import { TestFramework, TestType } from "./types.js";
import { GeneratedTestSuite, TestCase, TestAssertion, MockSetup } from "./test-generator.js";

/**
 * 测试框架适配器接口
 */
export interface TestFrameworkAdapter {
  /** 框架名称 */
  framework: TestFramework;
  /** 生成测试代码 */
  generateTestCode(testSuite: GeneratedTestSuite): string;
  /** 生成导入语句 */
  generateImports(testSuite: GeneratedTestSuite): string;
  /** 生成测试设置 */
  generateSetup(testSuite: GeneratedTestSuite): string;
  /** 生成测试清理 */
  generateTeardown(testSuite: GeneratedTestSuite): string;
  /** 生成断言代码 */
  generateAssertion(assertion: TestAssertion): string;
  /** 生成模拟代码 */
  generateMock(mock: MockSetup): string;
}

/**
 * 测试框架适配器工厂
 */
export class TestFrameworkAdapterFactory {
  private static adapters: Map<TestFramework, TestFrameworkAdapter> = new Map();

  /**
   * 注册适配器
   */
  static registerAdapter(framework: TestFramework, adapter: TestFrameworkAdapter) {
    this.adapters.set(framework, adapter);
  }

  /**
   * 获取适配器
   */
  static getAdapter(framework: TestFramework): TestFrameworkAdapter {
    const adapter = this.adapters.get(framework);
    if (!adapter) {
      throw new Error(`Unsupported test framework: ${framework}`);
    }
    return adapter;
  }

  /**
   * 初始化所有适配器
   */
  static initializeAdapters() {
    this.registerAdapter("jest", new JestAdapter());
    this.registerAdapter("vitest", new VitestAdapter());
    this.registerAdapter("mocha", new MochaAdapter());
    this.registerAdapter("cypress", new CypressAdapter());
    this.registerAdapter("playwright", new PlaywrightAdapter());
  }
}

/**
 * Jest 适配器
 */
export class JestAdapter implements TestFrameworkAdapter {
  framework: TestFramework = "jest";

  generateTestCode(testSuite: GeneratedTestSuite): string {
    const imports = this.generateImports(testSuite);
    const setup = this.generateSetup(testSuite);
    const teardown = this.generateTeardown(testSuite);
    const testCases = this.generateTestCases(testSuite.testCases);

    return `${imports}

${setup}

describe('${this.getDescribeName(testSuite.targetFile)}', () => {
${teardown}

${testCases}
});`;
  }

  generateImports(testSuite: GeneratedTestSuite): string {
    const targetModule = testSuite.targetFile.replace(/\.(ts|js)$/, '');
    return `import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import * as target from './${targetModule}';`;
  }

  generateSetup(testSuite: GeneratedTestSuite): string {
    if (testSuite.setup.beforeEach.length === 0) return '';
    
    return `beforeEach(() => {
${testSuite.setup.beforeEach.map(line => `  ${line}`).join('\n')}
});`;
  }

  generateTeardown(testSuite: GeneratedTestSuite): string {
    if (testSuite.teardown.afterEach.length === 0) return '';
    
    return `  afterEach(() => {
${testSuite.teardown.afterEach.map(line => `    ${line}`).join('\n')}
  });`;
  }

  generateTestCases(testCases: TestCase[]): string {
    return testCases.map(testCase => this.generateTestCase(testCase)).join('\n\n');
  }

  generateTestCase(testCase: TestCase): string {
    const mocks = testCase.mocks.map(mock => this.generateMock(mock)).join('\n    ');
    const assertions = testCase.assertions.map(assertion => this.generateAssertion(assertion)).join('\n    ');
    const inputs = this.generateInputs(testCase.inputs);

    return `  it('${testCase.name}', ${testCase.type === 'integration' ? 'async ' : ''}() => {
    // ${testCase.description}
${mocks ? `    ${mocks}\n` : ''}
    // Arrange
${inputs}

    // Act
    ${this.generateActCode(testCase)}

    // Assert
    ${assertions}
  });`;
  }

  generateAssertion(assertion: TestAssertion): string {
    switch (assertion.type) {
      case "equals":
        return `expect(${assertion.target}).toBe(${JSON.stringify(assertion.expected)});`;
      case "throws":
        return `expect(() => ${assertion.target}).toThrow(${assertion.expected});`;
      case "contains":
        return `expect(${assertion.target}).toContain(${JSON.stringify(assertion.expected)});`;
      case "called":
        return `expect(${assertion.target}).toHaveBeenCalled();`;
      case "calledWith":
        return `expect(${assertion.target}).toHaveBeenCalledWith(${JSON.stringify(assertion.expected)});`;
      default:
        return `expect(${assertion.target}).toBe(${JSON.stringify(assertion.expected)});`;
    }
  }

  generateMock(mock: MockSetup): string {
    switch (mock.type) {
      case "function":
        return `const ${mock.target} = jest.fn()${mock.returnValue ? `.mockReturnValue(${JSON.stringify(mock.returnValue)})` : ''};`;
      case "module":
        return `jest.mock('${mock.target}');`;
      case "class":
        return `const Mock${mock.target} = jest.fn().mockImplementation(() => ({}));`;
      default:
        return `const ${mock.target} = jest.fn();`;
    }
  }

  private getDescribeName(targetFile: string): string {
    return targetFile.split('/').pop()?.replace(/\.(ts|js)$/, '') || 'Unknown';
  }

  private generateInputs(inputs: any[]): string {
    if (inputs.length === 0) return '';
    return inputs.map(input => `    const ${input.name} = ${JSON.stringify(input.value)};`).join('\n');
  }

  private generateActCode(testCase: TestCase): string {
    if (testCase.type === 'integration') {
      return `const result = await target.${testCase.name.split(' ')[0]}(${testCase.inputs.map(i => i.name).join(', ')});`;
    }
    return `const result = target.${testCase.name.split(' ')[0]}(${testCase.inputs.map(i => i.name).join(', ')});`;
  }
}

/**
 * Vitest 适配器
 */
export class VitestAdapter implements TestFrameworkAdapter {
  framework: TestFramework = "vitest";

  generateTestCode(testSuite: GeneratedTestSuite): string {
    // Vitest 语法与 Jest 非常相似
    const jestAdapter = new JestAdapter();
    return jestAdapter.generateTestCode(testSuite)
      .replace('@jest/globals', 'vitest')
      .replace('jest.fn()', 'vi.fn()')
      .replace('jest.mock', 'vi.mock');
  }

  generateImports(testSuite: GeneratedTestSuite): string {
    const targetModule = testSuite.targetFile.replace(/\.(ts|js)$/, '');
    return `import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as target from './${targetModule}';`;
  }

  generateSetup(testSuite: GeneratedTestSuite): string {
    return new JestAdapter().generateSetup(testSuite);
  }

  generateTeardown(testSuite: GeneratedTestSuite): string {
    return new JestAdapter().generateTeardown(testSuite);
  }

  generateAssertion(assertion: TestAssertion): string {
    return new JestAdapter().generateAssertion(assertion);
  }

  generateMock(mock: MockSetup): string {
    return new JestAdapter().generateMock(mock).replace('jest.fn()', 'vi.fn()').replace('jest.mock', 'vi.mock');
  }
}

/**
 * Mocha + Chai 适配器
 */
export class MochaAdapter implements TestFrameworkAdapter {
  framework: TestFramework = "mocha";

  generateTestCode(testSuite: GeneratedTestSuite): string {
    const imports = this.generateImports(testSuite);
    const setup = this.generateSetup(testSuite);
    const teardown = this.generateTeardown(testSuite);
    const testCases = this.generateTestCases(testSuite.testCases);

    return `${imports}

${setup}

describe('${this.getDescribeName(testSuite.targetFile)}', () => {
${teardown}

${testCases}
});`;
  }

  generateImports(testSuite: GeneratedTestSuite): string {
    const targetModule = testSuite.targetFile.replace(/\.(ts|js)$/, '');
    return `import { describe, it, beforeEach, afterEach } from 'mocha';
import { expect } from 'chai';
import * as sinon from 'sinon';
import * as target from './${targetModule}';`;
  }

  generateSetup(testSuite: GeneratedTestSuite): string {
    if (testSuite.setup.beforeEach.length === 0) return '';
    
    return `beforeEach(() => {
${testSuite.setup.beforeEach.map(line => `  ${line}`).join('\n')}
});`;
  }

  generateTeardown(testSuite: GeneratedTestSuite): string {
    if (testSuite.teardown.afterEach.length === 0) return '';
    
    return `  afterEach(() => {
${testSuite.teardown.afterEach.map(line => `    ${line}`).join('\n')}
  });`;
  }

  generateTestCases(testCases: TestCase[]): string {
    return testCases.map(testCase => this.generateTestCase(testCase)).join('\n\n');
  }

  generateTestCase(testCase: TestCase): string {
    const mocks = testCase.mocks.map(mock => this.generateMock(mock)).join('\n    ');
    const assertions = testCase.assertions.map(assertion => this.generateAssertion(assertion)).join('\n    ');
    const inputs = this.generateInputs(testCase.inputs);

    return `  it('${testCase.name}', ${testCase.type === 'integration' ? 'async ' : ''}() => {
    // ${testCase.description}
${mocks ? `    ${mocks}\n` : ''}
    // Arrange
${inputs}

    // Act
    ${this.generateActCode(testCase)}

    // Assert
    ${assertions}
  });`;
  }

  generateAssertion(assertion: TestAssertion): string {
    switch (assertion.type) {
      case "equals":
        return `expect(${assertion.target}).to.equal(${JSON.stringify(assertion.expected)});`;
      case "throws":
        return `expect(() => ${assertion.target}).to.throw(${assertion.expected});`;
      case "contains":
        return `expect(${assertion.target}).to.include(${JSON.stringify(assertion.expected)});`;
      case "called":
        return `expect(${assertion.target}).to.have.been.called;`;
      case "calledWith":
        return `expect(${assertion.target}).to.have.been.calledWith(${JSON.stringify(assertion.expected)});`;
      default:
        return `expect(${assertion.target}).to.equal(${JSON.stringify(assertion.expected)});`;
    }
  }

  generateMock(mock: MockSetup): string {
    switch (mock.type) {
      case "function":
        return `const ${mock.target} = sinon.stub()${mock.returnValue ? `.returns(${JSON.stringify(mock.returnValue)})` : ''};`;
      case "module":
        return `// Mock module: ${mock.target}`;
      case "class":
        return `const Mock${mock.target} = sinon.createStubInstance(${mock.target});`;
      default:
        return `const ${mock.target} = sinon.stub();`;
    }
  }

  private getDescribeName(targetFile: string): string {
    return targetFile.split('/').pop()?.replace(/\.(ts|js)$/, '') || 'Unknown';
  }

  private generateInputs(inputs: any[]): string {
    if (inputs.length === 0) return '';
    return inputs.map(input => `    const ${input.name} = ${JSON.stringify(input.value)};`).join('\n');
  }

  private generateActCode(testCase: TestCase): string {
    if (testCase.type === 'integration') {
      return `const result = await target.${testCase.name.split(' ')[0]}(${testCase.inputs.map(i => i.name).join(', ')});`;
    }
    return `const result = target.${testCase.name.split(' ')[0]}(${testCase.inputs.map(i => i.name).join(', ')});`;
  }
}

/**
 * Cypress 适配器
 */
export class CypressAdapter implements TestFrameworkAdapter {
  framework: TestFramework = "cypress";

  generateTestCode(testSuite: GeneratedTestSuite): string {
    const imports = this.generateImports(testSuite);
    const testCases = this.generateTestCases(testSuite.testCases);

    return `${imports}

describe('${this.getDescribeName(testSuite.targetFile)} E2E Tests', () => {
${testCases}
});`;
  }

  generateImports(testSuite: GeneratedTestSuite): string {
    return `/// <reference types="cypress" />`;
  }

  generateSetup(testSuite: GeneratedTestSuite): string {
    return `beforeEach(() => {
    cy.visit('/');
  });`;
  }

  generateTeardown(testSuite: GeneratedTestSuite): string {
    return '';
  }

  generateTestCases(testCases: TestCase[]): string {
    return testCases.map(testCase => this.generateTestCase(testCase)).join('\n\n');
  }

  generateTestCase(testCase: TestCase): string {
    const assertions = testCase.assertions.map(assertion => this.generateAssertion(assertion)).join('\n    ');

    return `  it('${testCase.name}', () => {
    // ${testCase.description}
    
    // 执行操作
    ${this.generateCypressActions(testCase)}
    
    // 验证结果
    ${assertions}
  });`;
  }

  generateAssertion(assertion: TestAssertion): string {
    switch (assertion.type) {
      case "equals":
        return `cy.get('${assertion.target}').should('contain', '${assertion.expected}');`;
      case "contains":
        return `cy.get('${assertion.target}').should('contain', '${assertion.expected}');`;
      default:
        return `cy.get('${assertion.target}').should('exist');`;
    }
  }

  generateMock(mock: MockSetup): string {
    return `cy.intercept('${mock.target}', ${JSON.stringify(mock.returnValue)});`;
  }

  private getDescribeName(targetFile: string): string {
    return targetFile.split('/').pop()?.replace(/\.(ts|js)$/, '') || 'Unknown';
  }

  private generateCypressActions(testCase: TestCase): string {
    // 简化实现：生成基本的 Cypress 操作
    return `cy.get('[data-testid="test-element"]').click();`;
  }
}

/**
 * Playwright 适配器
 */
export class PlaywrightAdapter implements TestFrameworkAdapter {
  framework: TestFramework = "playwright";

  generateTestCode(testSuite: GeneratedTestSuite): string {
    const imports = this.generateImports(testSuite);
    const testCases = this.generateTestCases(testSuite.testCases);

    return `${imports}

test.describe('${this.getDescribeName(testSuite.targetFile)} E2E Tests', () => {
${testCases}
});`;
  }

  generateImports(testSuite: GeneratedTestSuite): string {
    return `import { test, expect } from '@playwright/test';`;
  }

  generateSetup(testSuite: GeneratedTestSuite): string {
    return `test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });`;
  }

  generateTeardown(testSuite: GeneratedTestSuite): string {
    return '';
  }

  generateTestCases(testCases: TestCase[]): string {
    return testCases.map(testCase => this.generateTestCase(testCase)).join('\n\n');
  }

  generateTestCase(testCase: TestCase): string {
    const assertions = testCase.assertions.map(assertion => this.generateAssertion(assertion)).join('\n    ');

    return `  test('${testCase.name}', async ({ page }) => {
    // ${testCase.description}
    
    // 执行操作
    ${this.generatePlaywrightActions(testCase)}
    
    // 验证结果
    ${assertions}
  });`;
  }

  generateAssertion(assertion: TestAssertion): string {
    switch (assertion.type) {
      case "equals":
        return `await expect(page.locator('${assertion.target}')).toHaveText('${assertion.expected}');`;
      case "contains":
        return `await expect(page.locator('${assertion.target}')).toContainText('${assertion.expected}');`;
      default:
        return `await expect(page.locator('${assertion.target}')).toBeVisible();`;
    }
  }

  generateMock(mock: MockSetup): string {
    return `await page.route('${mock.target}', route => route.fulfill({ json: ${JSON.stringify(mock.returnValue)} }));`;
  }

  private getDescribeName(targetFile: string): string {
    return targetFile.split('/').pop()?.replace(/\.(ts|js)$/, '') || 'Unknown';
  }

  private generatePlaywrightActions(testCase: TestCase): string {
    // 简化实现：生成基本的 Playwright 操作
    return `await page.click('[data-testid="test-element"]');`;
  }
}

// 初始化所有适配器
TestFrameworkAdapterFactory.initializeAdapters();
