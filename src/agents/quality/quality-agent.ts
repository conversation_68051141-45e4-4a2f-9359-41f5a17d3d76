/**
 * Quality Agent - 质量代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import { 
  AgentMessage, 
  AgentContext, 
  QualityState,
  CodeAnalysisResult,
  SecurityScanResult,
  PerformanceAssessment
} from '../types.js';

export class QualityAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('quality', context);
  }

  protected async onStart(): Promise<void> {
    console.log('🔍 Quality Agent ready to analyze quality');
  }

  protected async onStop(): Promise<void> {
    console.log('🔍 Quality Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'analyze-quality':
        return await this.analyzeQuality(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async analyzeQuality(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Analyzing code quality and security');
    
    try {
      console.log(`🔍 Analyzing quality for implementation`);
      
      const codeAnalysis = await this.performCodeAnalysis(data);
      const securityScan = await this.performSecurityScan(data);
      const performanceAssessment = await this.performPerformanceAssessment(data);
      const overallScore = await this.calculateOverallQualityScore(codeAnalysis, securityScan, performanceAssessment);

      const qualityState: QualityState = {
        codeAnalysis,
        securityScan,
        performanceAssessment,
        overallScore
      };

      // 更新共享状态
      this.updateSharedState({ quality: qualityState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('quality', overallScore);

      console.log(`🔍 Quality analysis completed with score: ${overallScore}%`);

      return {
        id: `quality-response-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'quality',
          success: qualityPassed,
          result: qualityState,
          qualityScore: overallScore,
          message: qualityPassed ? 'Quality analysis passed' : 'Quality issues detected'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error analyzing quality:', error);
      
      return {
        id: `quality-error-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'quality',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  private async performCodeAnalysis(data: any): Promise<CodeAnalysisResult> {
    console.log('📊 Performing code analysis...');
    
    const files = data.implementation?.generatedFiles || [];
    
    // 模拟代码分析
    const complexity = Math.max(1, Math.min(10, files.length * 2));
    const maintainability = files.length > 0 ? 
      files.reduce((sum: number, file: any) => sum + file.qualityScore, 0) / files.length : 80;
    
    return {
      complexity,
      maintainability,
      duplications: Math.floor(Math.random() * 5), // 0-4% 重复率
      issues: [
        {
          file: 'src/services/AuthService.ts',
          line: 15,
          severity: 'minor',
          type: 'code-smell',
          message: 'Consider extracting this magic number to a constant'
        }
      ],
      metrics: {
        linesOfCode: files.length * 50,
        cyclomaticComplexity: complexity,
        maintainabilityIndex: maintainability,
        technicalDebt: Math.floor(Math.random() * 2) // 0-1 hours
      }
    };
  }

  private async performSecurityScan(data: any): Promise<SecurityScanResult> {
    console.log('🔒 Performing security scan...');
    
    const isAuthSystem = data.requirements?.title?.toLowerCase().includes('认证') || 
                        data.requirements?.title?.toLowerCase().includes('auth');
    
    const vulnerabilities = [];
    
    if (isAuthSystem) {
      // 认证系统的安全检查
      vulnerabilities.push({
        type: 'weak-crypto',
        severity: 'medium' as const,
        file: 'src/services/AuthService.ts',
        line: 25,
        description: 'JWT secret should be stored in environment variables',
        solution: 'Move JWT secret to environment configuration'
      });
    }

    const riskScore = vulnerabilities.length === 0 ? 95 : 
                     vulnerabilities.every(v => v.severity === 'low') ? 85 :
                     vulnerabilities.some(v => v.severity === 'medium') ? 75 : 60;

    return {
      vulnerabilities,
      riskScore,
      recommendations: [
        'Use environment variables for sensitive configuration',
        'Implement rate limiting for authentication endpoints',
        'Add input validation and sanitization'
      ]
    };
  }

  private async performPerformanceAssessment(data: any): Promise<PerformanceAssessment> {
    console.log('⚡ Performing performance assessment...');
    
    const files = data.implementation?.generatedFiles || [];
    
    return {
      bottlenecks: [
        {
          type: 'database-query',
          location: 'AuthService.login',
          impact: 'low',
          description: 'Linear search through users array'
        }
      ],
      optimizations: [
        {
          type: 'caching',
          description: 'Implement Redis caching for user sessions',
          effort: 4,
          impact: 8,
          priority: 'medium'
        },
        {
          type: 'indexing',
          description: 'Add database index on email field',
          effort: 2,
          impact: 6,
          priority: 'high'
        }
      ],
      score: Math.min(95, 70 + files.length * 5) // 基础70分，每个文件+5分
    };
  }

  private async calculateOverallQualityScore(
    codeAnalysis: CodeAnalysisResult,
    securityScan: SecurityScanResult,
    performanceAssessment: PerformanceAssessment
  ): Promise<number> {
    let score = 0;
    
    // 代码质量评分 (40%)
    const codeScore = Math.max(0, 100 - codeAnalysis.complexity * 5 - codeAnalysis.duplications * 10);
    score += codeScore * 0.4;
    
    // 安全评分 (35%)
    score += securityScan.riskScore * 0.35;
    
    // 性能评分 (25%)
    score += performanceAssessment.score * 0.25;
    
    return Math.round(score);
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.analyzeQuality(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'analyze-quality', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'Code quality analysis',
      'Security vulnerability scanning',
      'Performance bottleneck detection',
      'Technical debt measurement',
      'Best practices validation',
      'Quality metrics calculation'
    ];
  }
}
