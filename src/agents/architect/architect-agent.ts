/**
 * Architect Agent - 架构代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import { 
  AgentMessage, 
  AgentContext, 
  ArchitectureState,
  TechnicalStack,
  ApiDesign,
  DataModel
} from '../types.js';

export class ArchitectAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('architect', context);
  }

  protected async onStart(): Promise<void> {
    console.log('🏗️ Architect Agent ready to design architecture');
  }

  protected async onStop(): Promise<void> {
    console.log('🏗️ Architect Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'design-architecture':
        return await this.designArchitecture(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async designArchitecture(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Designing system architecture');
    
    try {
      console.log(`🏗️ Designing architecture for: ${data.requirements?.title}`);
      
      const technicalStack = await this.selectTechnicalStack(data);
      const apiDesign = await this.designApi(data);
      const dataModel = await this.designDataModel(data);
      const designDocument = await this.generateDesignDocument(data, technicalStack, apiDesign, dataModel);
      const qualityScore = await this.calculateArchitectureQuality(technicalStack, apiDesign, dataModel);

      const architectureState: ArchitectureState = {
        designDocument,
        technicalStack,
        apiDesign,
        dataModel,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ architecture: architectureState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('architecture', qualityScore);

      console.log(`🏗️ Architecture designed with quality score: ${qualityScore}%`);

      return {
        id: `arch-response-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'architecture',
          success: qualityPassed,
          result: architectureState,
          qualityScore,
          message: qualityPassed ? 'Architecture designed successfully' : 'Architecture quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error designing architecture:', error);
      
      return {
        id: `arch-error-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'architecture',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  private async selectTechnicalStack(data: any): Promise<TechnicalStack> {
    console.log('🛠️ Selecting technical stack...');
    
    // 基于需求选择技术栈
    const isAuthSystem = data.requirements?.title?.toLowerCase().includes('认证') || 
                        data.requirements?.title?.toLowerCase().includes('auth');

    return {
      frontend: ['React', 'TypeScript', 'Tailwind CSS'],
      backend: ['Node.js', 'Express', 'TypeScript'],
      database: isAuthSystem ? ['PostgreSQL', 'Redis'] : ['MongoDB'],
      testing: ['Jest', 'Supertest', 'React Testing Library'],
      deployment: ['Docker', 'GitHub Actions', 'Vercel']
    };
  }

  private async designApi(data: any): Promise<ApiDesign> {
    console.log('🔌 Designing API...');
    
    const isAuthSystem = data.requirements?.title?.toLowerCase().includes('认证') || 
                        data.requirements?.title?.toLowerCase().includes('auth');

    if (isAuthSystem) {
      return {
        endpoints: [
          {
            method: 'POST',
            path: '/api/auth/register',
            description: 'User registration',
            parameters: [
              { name: 'email', type: 'string', required: true, description: 'User email' },
              { name: 'password', type: 'string', required: true, description: 'User password' }
            ],
            responses: [
              { statusCode: 201, description: 'User created successfully' },
              { statusCode: 400, description: 'Invalid input' }
            ]
          },
          {
            method: 'POST',
            path: '/api/auth/login',
            description: 'User login',
            parameters: [
              { name: 'email', type: 'string', required: true, description: 'User email' },
              { name: 'password', type: 'string', required: true, description: 'User password' }
            ],
            responses: [
              { statusCode: 200, description: 'Login successful' },
              { statusCode: 401, description: 'Invalid credentials' }
            ]
          },
          {
            method: 'POST',
            path: '/api/auth/logout',
            description: 'User logout',
            parameters: [],
            responses: [
              { statusCode: 200, description: 'Logout successful' }
            ]
          }
        ],
        authentication: 'jwt',
        dataFormats: ['JSON']
      };
    }

    // 通用 API 设计
    return {
      endpoints: [
        {
          method: 'GET',
          path: '/api/health',
          description: 'Health check',
          parameters: [],
          responses: [
            { statusCode: 200, description: 'Service healthy' }
          ]
        }
      ],
      authentication: 'api-key',
      dataFormats: ['JSON']
    };
  }

  private async designDataModel(data: any): Promise<DataModel> {
    console.log('🗄️ Designing data model...');
    
    const isAuthSystem = data.requirements?.title?.toLowerCase().includes('认证') || 
                        data.requirements?.title?.toLowerCase().includes('auth');

    if (isAuthSystem) {
      return {
        entities: [
          {
            name: 'User',
            fields: [
              { name: 'id', type: 'UUID', required: true, unique: true, description: 'Primary key' },
              { name: 'email', type: 'string', required: true, unique: true, description: 'User email' },
              { name: 'password', type: 'string', required: true, unique: false, description: 'Hashed password' },
              { name: 'createdAt', type: 'timestamp', required: true, unique: false, description: 'Creation time' },
              { name: 'updatedAt', type: 'timestamp', required: true, unique: false, description: 'Last update time' }
            ],
            constraints: [
              { type: 'primary-key', fields: ['id'], description: 'Primary key constraint' },
              { type: 'unique', fields: ['email'], description: 'Unique email constraint' }
            ]
          },
          {
            name: 'Session',
            fields: [
              { name: 'id', type: 'UUID', required: true, unique: true, description: 'Session ID' },
              { name: 'userId', type: 'UUID', required: true, unique: false, description: 'User reference' },
              { name: 'token', type: 'string', required: true, unique: true, description: 'JWT token' },
              { name: 'expiresAt', type: 'timestamp', required: true, unique: false, description: 'Expiration time' }
            ],
            constraints: [
              { type: 'primary-key', fields: ['id'], description: 'Primary key constraint' },
              { type: 'foreign-key', fields: ['userId'], description: 'Foreign key to User' }
            ]
          }
        ],
        relationships: [
          {
            from: 'User',
            to: 'Session',
            type: 'one-to-many',
            description: 'User can have multiple sessions'
          }
        ]
      };
    }

    // 通用数据模型
    return {
      entities: [
        {
          name: 'Entity',
          fields: [
            { name: 'id', type: 'UUID', required: true, unique: true, description: 'Primary key' },
            { name: 'name', type: 'string', required: true, unique: false, description: 'Entity name' },
            { name: 'createdAt', type: 'timestamp', required: true, unique: false, description: 'Creation time' }
          ],
          constraints: [
            { type: 'primary-key', fields: ['id'], description: 'Primary key constraint' }
          ]
        }
      ],
      relationships: []
    };
  }

  private async generateDesignDocument(
    data: any, 
    technicalStack: TechnicalStack, 
    apiDesign: ApiDesign, 
    dataModel: DataModel
  ): Promise<string> {
    return `# System Architecture Design

## Overview
${data.requirements?.description || 'System architecture design'}

## Technical Stack
- **Frontend**: ${technicalStack.frontend.join(', ')}
- **Backend**: ${technicalStack.backend.join(', ')}
- **Database**: ${technicalStack.database.join(', ')}
- **Testing**: ${technicalStack.testing.join(', ')}
- **Deployment**: ${technicalStack.deployment.join(', ')}

## API Design
- **Authentication**: ${apiDesign.authentication}
- **Data Formats**: ${apiDesign.dataFormats.join(', ')}
- **Endpoints**: ${apiDesign.endpoints.length} endpoints defined

## Data Model
- **Entities**: ${dataModel.entities.length} entities
- **Relationships**: ${dataModel.relationships.length} relationships

## Quality Attributes
- **Scalability**: Horizontal scaling support
- **Security**: JWT authentication, input validation
- **Performance**: Optimized database queries, caching
- **Maintainability**: Clean architecture, SOLID principles
`;
  }

  private async calculateArchitectureQuality(
    technicalStack: TechnicalStack, 
    apiDesign: ApiDesign, 
    dataModel: DataModel
  ): Promise<number> {
    let score = 0;
    
    // 技术栈合理性评分
    const stackScore = technicalStack.frontend.length > 0 && 
                      technicalStack.backend.length > 0 && 
                      technicalStack.database.length > 0 ? 100 : 70;
    score += stackScore * 0.3; // 30%权重

    // API 设计质量评分
    const apiScore = apiDesign.endpoints.length >= 3 ? 100 : 
                    apiDesign.endpoints.length >= 1 ? 80 : 50;
    score += apiScore * 0.3; // 30%权重

    // 数据模型质量评分
    const dataScore = dataModel.entities.length > 0 ? 100 : 50;
    score += dataScore * 0.2; // 20%权重

    // 安全性评分
    const securityScore = apiDesign.authentication !== 'basic' ? 100 : 70;
    score += securityScore * 0.2; // 20%权重

    return Math.round(score);
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.designArchitecture(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'design-architecture', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'System architecture design',
      'Technical stack selection',
      'API design and specification',
      'Data model design',
      'Architecture quality assessment',
      'Scalability and performance planning'
    ];
  }
}
