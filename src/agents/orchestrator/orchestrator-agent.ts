/**
 * Orchestrator Agent - 编排代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  WorkflowPhase,
  WorkflowResult,
  PhaseResult,
  QualityReport
} from '../types.js';

export class OrchestratorAgent extends BaseAgent {
  private workflowPhases: WorkflowPhase[] = [
    'initialization',
    'requirements',
    'architecture',
    'implementation',
    'quality',
    'testing',
    'integration',
    'completion'
  ];

  private currentPhaseIndex: number = 0;
  private phaseResults: PhaseResult[] = [];
  private workflowStartTime: number = 0;

  constructor(context: AgentContext) {
    super('orchestrator', context);
  }

  protected async onStart(): Promise<void> {
    console.log('🎯 Orchestrator Agent ready to coordinate workflow');
  }

  protected async onStop(): Promise<void> {
    console.log('🎯 Orchestrator Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      case 'response':
        return await this.handleResponse(message);
      case 'notification':
        return await this.handleNotification(message);
      default:
        console.log(`🎯 Orchestrator received unknown message type: ${message.type}`);
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'start-workflow':
        return await this.startWorkflow(data);
      case 'get-status':
        return await this.getWorkflowStatus(message);
      case 'abort-workflow':
        return await this.abortWorkflow(message);
      default:
        return {
          id: `response-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async handleResponse(message: AgentMessage): Promise<AgentMessage | null> {
    const { phase, success, result, qualityScore } = message.payload;

    // 记录阶段结果
    const phaseResult: PhaseResult = {
      phase,
      agent: message.from,
      success,
      duration: Date.now() - this.workflowStartTime,
      qualityScore: qualityScore || 0,
      outputs: result ? [result] : [],
      errors: success ? [] : [message.payload.error || 'Unknown error']
    };

    this.phaseResults.push(phaseResult);

    if (success) {
      console.log(`✅ Phase ${phase} completed successfully by ${message.from}`);
      await this.proceedToNextPhase();
    } else {
      console.log(`❌ Phase ${phase} failed by ${message.from}`);
      await this.handlePhaseFailure(phaseResult);
    }

    return null;
  }

  private async handleNotification(message: AgentMessage): Promise<AgentMessage | null> {
    console.log(`📢 Orchestrator received notification from ${message.from}: ${message.payload.message}`);
    return null;
  }

  private async startWorkflow(data: any): Promise<AgentMessage> {
    this.workflowStartTime = Date.now();
    this.currentPhaseIndex = 0;
    this.phaseResults = [];

    console.log(`🚀 Starting workflow: ${data.description}`);

    // 更新共享状态
    this.updateSharedState({
      requirements: {
        title: data.title,
        description: data.description,
        qualityScore: 0
      }
    });

    // 开始第一个阶段
    await this.executeCurrentPhase();

    return {
      id: `response-${Date.now()}`,
      type: 'response',
      to: 'user',
      payload: {
        success: true,
        message: 'Workflow started successfully',
        workflowId: this.context.workflowId,
        currentPhase: this.getCurrentPhase()
      },
      timestamp: new Date().toISOString()
    };
  }

  private async executeCurrentPhase(): Promise<void> {
    const currentPhase = this.getCurrentPhase();
    console.log(`🔄 Executing phase: ${currentPhase}`);

    this.context.currentPhase = currentPhase;

    switch (currentPhase) {
      case 'initialization':
        await this.executeInitializationPhase();
        break;
      case 'requirements':
        await this.executeRequirementsPhase();
        break;
      case 'architecture':
        await this.executeArchitecturePhase();
        break;
      case 'implementation':
        await this.executeImplementationPhase();
        break;
      case 'quality':
        await this.executeQualityPhase();
        break;
      case 'testing':
        await this.executeTestingPhase();
        break;
      case 'integration':
        await this.executeIntegrationPhase();
        break;
      case 'completion':
        await this.executeCompletionPhase();
        break;
    }
  }

  private async executeInitializationPhase(): Promise<void> {
    this.setCurrentTask('Initializing workflow environment');

    // 初始化完成，直接进入下一阶段
    setTimeout(async () => {
      await this.sendMessage({
        id: `init-complete-${Date.now()}`,
        type: 'response',
        to: this.agentId,
        payload: {
          phase: 'initialization',
          success: true,
          result: 'Workflow environment initialized',
          qualityScore: 100
        }
      });
    }, 100);
  }

  private async executeRequirementsPhase(): Promise<void> {
    this.setCurrentTask('Generating requirements specification');

    await this.sendMessage({
      id: `req-task-${Date.now()}`,
      type: 'request',
      to: 'spec',
      payload: {
        action: 'generate-requirements',
        data: this.context.sharedState.requirements
      }
    });
  }

  private async executeArchitecturePhase(): Promise<void> {
    this.setCurrentTask('Designing system architecture');

    await this.sendMessage({
      id: `arch-task-${Date.now()}`,
      type: 'request',
      to: 'architect',
      payload: {
        action: 'design-architecture',
        data: this.context.sharedState
      }
    });
  }

  private async executeImplementationPhase(): Promise<void> {
    this.setCurrentTask('Implementing code');

    await this.sendMessage({
      id: `dev-task-${Date.now()}`,
      type: 'request',
      to: 'developer',
      payload: {
        action: 'implement-code',
        data: this.context.sharedState
      }
    });
  }

  private async executeQualityPhase(): Promise<void> {
    this.setCurrentTask('Performing quality analysis');

    await this.sendMessage({
      id: `quality-task-${Date.now()}`,
      type: 'request',
      to: 'quality',
      payload: {
        action: 'analyze-quality',
        data: this.context.sharedState
      }
    });
  }

  private async executeTestingPhase(): Promise<void> {
    this.setCurrentTask('Generating tests');

    await this.sendMessage({
      id: `test-task-${Date.now()}`,
      type: 'request',
      to: 'test',
      payload: {
        action: 'generate-tests',
        data: this.context.sharedState
      }
    });
  }

  private async executeIntegrationPhase(): Promise<void> {
    this.setCurrentTask('Integrating and validating');

    // 集成阶段：验证所有组件
    const overallQuality = this.calculateOverallQuality();

    // 获取当前质量门控阈值
    const integrationGate = this.context.qualityGates.find(gate => gate.phase === 'integration');
    const threshold = integrationGate?.threshold || 85;

    setTimeout(async () => {
      await this.sendMessage({
        id: `integration-complete-${Date.now()}`,
        type: 'response',
        to: this.agentId,
        payload: {
          phase: 'integration',
          success: overallQuality >= threshold,
          result: 'Integration completed',
          qualityScore: overallQuality
        }
      });
    }, 1000);
  }

  private async executeCompletionPhase(): Promise<void> {
    this.setCurrentTask('Finalizing workflow');

    const workflowResult = this.generateWorkflowResult();

    console.log('🎉 Workflow completed successfully!');
    console.log(`📊 Overall Quality Score: ${workflowResult.qualityReport.overallScore}%`);
    console.log(`⏱️ Total Duration: ${workflowResult.duration}ms`);

    await this.sendMessage({
      id: `workflow-complete-${Date.now()}`,
      type: 'notification',
      to: 'broadcast',
      payload: {
        message: 'Workflow completed successfully',
        result: workflowResult
      }
    });
  }

  private async proceedToNextPhase(): Promise<void> {
    this.currentPhaseIndex++;
    this.retryCount = 0; // 重置重试计数器

    if (this.currentPhaseIndex < this.workflowPhases.length) {
      await this.executeCurrentPhase();
    }
  }

  private retryCount: number = 0;
  private maxRetries: number = 3;

  private async handlePhaseFailure(phaseResult: PhaseResult): Promise<void> {
    console.log(`❌ Phase ${phaseResult.phase} failed, attempting recovery...`);

    // 限制重试次数的重试机制
    if (this.retryCount < this.maxRetries && phaseResult.qualityScore > 70) {
      this.retryCount++;
      console.log(`🔄 Retrying phase with optimizations... (${this.retryCount}/${this.maxRetries})`);
      await this.executeCurrentPhase();
    } else {
      console.log('💥 Workflow failed due to critical quality issues or max retries exceeded');
      await this.abortWorkflow();
    }
  }

  private getCurrentPhase(): WorkflowPhase {
    return this.workflowPhases[this.currentPhaseIndex];
  }

  private calculateOverallQuality(): number {
    const scores = this.phaseResults
      .filter(result => result.success)
      .map(result => result.qualityScore);

    if (scores.length === 0) return 0;

    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  private generateWorkflowResult(): WorkflowResult {
    const duration = Date.now() - this.workflowStartTime;
    const overallScore = this.calculateOverallQuality();

    return {
      success: true,
      workflowId: this.context.workflowId,
      duration,
      phases: this.phaseResults,
      finalState: this.context.sharedState,
      qualityReport: {
        overallScore,
        phaseScores: this.phaseResults.reduce((acc, result) => {
          acc[result.phase] = result.qualityScore;
          return acc;
        }, {} as { [phase: string]: number }),
        gatesPassed: this.phaseResults.filter(r => r.success).length,
        gatesTotal: this.phaseResults.length,
        recommendations: []
      }
    };
  }

  private async getWorkflowStatus(message: AgentMessage): Promise<AgentMessage> {
    return {
      id: `status-${Date.now()}`,
      type: 'response',
      to: message.from,
      payload: {
        workflowId: this.context.workflowId,
        currentPhase: this.getCurrentPhase(),
        progress: (this.currentPhaseIndex / this.workflowPhases.length) * 100,
        phaseResults: this.phaseResults,
        overallQuality: this.calculateOverallQuality()
      },
      timestamp: new Date().toISOString(),
      correlationId: message.correlationId
    };
  }

  private async abortWorkflow(message?: AgentMessage): Promise<AgentMessage | null> {
    console.log('🛑 Aborting workflow...');

    await this.sendMessage({
      id: `abort-${Date.now()}`,
      type: 'notification',
      to: 'broadcast',
      payload: {
        message: 'Workflow aborted',
        reason: 'Quality threshold not met or manual abort'
      }
    });

    if (message) {
      return {
        id: `abort-response-${Date.now()}`,
        type: 'response',
        to: message.from,
        payload: {
          success: true,
          message: 'Workflow aborted successfully'
        },
        timestamp: new Date().toISOString(),
        correlationId: message.correlationId
      };
    }

    return null;
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.startWorkflow(taskData);
  }

  getCapabilities(): string[] {
    return [
      'Workflow orchestration and coordination',
      'Phase management and progression',
      'Quality gate enforcement',
      'Agent communication coordination',
      'Progress monitoring and reporting',
      'Error handling and recovery'
    ];
  }
}
