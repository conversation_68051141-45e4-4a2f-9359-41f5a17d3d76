/**
 * Sub-Agents 基础代理类
 * v1.4.0 - Sub-Agents 革命
 */

import { EventEmitter } from 'events';
import { 
  AgentMessage, 
  AgentContext, 
  AgentType, 
  WorkflowPhase,
  QualityGate 
} from './types.js';

export abstract class BaseAgent extends EventEmitter {
  protected agentId: string;
  protected agentType: AgentType;
  protected context: AgentContext;
  protected isActive: boolean = false;
  protected currentTask: string | null = null;

  constructor(agentType: AgentType, context: AgentContext) {
    super();
    this.agentType = agentType;
    this.agentId = `${agentType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    this.context = context;
    
    console.log(`🤖 ${this.getDisplayName()} initialized`);
  }

  /**
   * 获取代理显示名称
   */
  protected getDisplayName(): string {
    const names = {
      orchestrator: '🎯 Orchestrator Agent',
      spec: '📋 Spec Agent',
      architect: '🏗️ Architect Agent',
      developer: '💻 Developer Agent',
      quality: '🔍 Quality Agent',
      test: '🧪 Test Agent'
    };
    return names[this.agentType] || `🤖 ${this.agentType} Agent`;
  }

  /**
   * 启动代理
   */
  async start(): Promise<void> {
    if (this.isActive) {
      throw new Error(`${this.getDisplayName()} is already active`);
    }
    
    this.isActive = true;
    console.log(`🚀 ${this.getDisplayName()} started`);
    
    await this.onStart();
  }

  /**
   * 停止代理
   */
  async stop(): Promise<void> {
    if (!this.isActive) {
      return;
    }
    
    this.isActive = false;
    this.currentTask = null;
    console.log(`⏹️ ${this.getDisplayName()} stopped`);
    
    await this.onStop();
  }

  /**
   * 处理消息
   */
  async handleMessage(message: AgentMessage): Promise<AgentMessage | null> {
    if (!this.isActive) {
      throw new Error(`${this.getDisplayName()} is not active`);
    }

    console.log(`📨 ${this.getDisplayName()} received message: ${message.type} from ${message.from}`);
    
    try {
      const response = await this.processMessage(message);
      
      if (response) {
        response.from = this.agentId;
        response.timestamp = new Date().toISOString();
        console.log(`📤 ${this.getDisplayName()} sending response: ${response.type} to ${response.to}`);
      }
      
      return response;
    } catch (error) {
      console.error(`❌ ${this.getDisplayName()} error processing message:`, error);
      
      return {
        id: `error-${Date.now()}`,
        type: 'error',
        from: this.agentId,
        to: message.from,
        payload: {
          error: error instanceof Error ? error.message : String(error),
          originalMessage: message
        },
        timestamp: new Date().toISOString(),
        correlationId: message.correlationId
      };
    }
  }

  /**
   * 发送消息
   */
  protected async sendMessage(message: Omit<AgentMessage, 'from' | 'timestamp'>): Promise<void> {
    const fullMessage: AgentMessage = {
      ...message,
      from: this.agentId,
      timestamp: new Date().toISOString()
    };
    
    console.log(`📤 ${this.getDisplayName()} sending message: ${message.type} to ${message.to}`);
    this.emit('message', fullMessage);
  }

  /**
   * 更新共享状态
   */
  protected updateSharedState(updates: Partial<any>): void {
    Object.assign(this.context.sharedState, updates);
    console.log(`🔄 ${this.getDisplayName()} updated shared state`);
  }

  /**
   * 检查质量门控
   */
  protected async checkQualityGates(phase: WorkflowPhase, score: number): Promise<boolean> {
    const gates = this.context.qualityGates.filter(gate => gate.phase === phase);
    
    for (const gate of gates) {
      if (gate.required && score < gate.threshold) {
        console.log(`❌ ${this.getDisplayName()} failed quality gate: ${gate.name} (${score} < ${gate.threshold})`);
        return false;
      }
    }
    
    console.log(`✅ ${this.getDisplayName()} passed all quality gates for phase: ${phase}`);
    return true;
  }

  /**
   * 设置当前任务
   */
  protected setCurrentTask(task: string): void {
    this.currentTask = task;
    console.log(`📋 ${this.getDisplayName()} current task: ${task}`);
  }

  /**
   * 获取当前任务
   */
  getCurrentTask(): string | null {
    return this.currentTask;
  }

  /**
   * 获取代理状态
   */
  getStatus(): {
    agentId: string;
    agentType: AgentType;
    isActive: boolean;
    currentTask: string | null;
    displayName: string;
  } {
    return {
      agentId: this.agentId,
      agentType: this.agentType,
      isActive: this.isActive,
      currentTask: this.currentTask,
      displayName: this.getDisplayName()
    };
  }

  // 抽象方法，子类必须实现

  /**
   * 代理启动时的初始化逻辑
   */
  protected abstract onStart(): Promise<void>;

  /**
   * 代理停止时的清理逻辑
   */
  protected abstract onStop(): Promise<void>;

  /**
   * 处理具体的消息逻辑
   */
  protected abstract processMessage(message: AgentMessage): Promise<AgentMessage | null>;

  /**
   * 执行代理的主要任务
   */
  abstract executeTask(taskData: any): Promise<any>;

  /**
   * 获取代理能力描述
   */
  abstract getCapabilities(): string[];
}

/**
 * 代理工厂类
 */
export class AgentFactory {
  static async createAgent(agentType: AgentType, context: AgentContext): Promise<BaseAgent> {
    switch (agentType) {
      case 'orchestrator':
        const { OrchestratorAgent } = await import('./orchestrator/orchestrator-agent.js');
        return new OrchestratorAgent(context);
      
      case 'spec':
        const { SpecAgent } = await import('./spec/spec-agent.js');
        return new SpecAgent(context);
      
      case 'architect':
        const { ArchitectAgent } = await import('./architect/architect-agent.js');
        return new ArchitectAgent(context);
      
      case 'developer':
        const { DeveloperAgent } = await import('./developer/developer-agent.js');
        return new DeveloperAgent(context);
      
      case 'quality':
        const { QualityAgent } = await import('./quality/quality-agent.js');
        return new QualityAgent(context);
      
      case 'test':
        const { TestAgent } = await import('./test/test-agent.js');
        return new TestAgent(context);
      
      default:
        throw new Error(`Unknown agent type: ${agentType}`);
    }
  }
}
