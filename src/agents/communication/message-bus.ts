/**
 * Sub-Agents 消息总线
 * v1.4.0 - Sub-Agents 革命
 */

import { EventEmitter } from 'events';
import { AgentMessage, AgentType } from '../types.js';
import { BaseAgent } from '../base-agent.js';

export class MessageBus extends EventEmitter {
  private agents: Map<string, BaseAgent> = new Map();
  private messageHistory: AgentMessage[] = [];
  private maxHistorySize: number = 1000;

  constructor() {
    super();
    console.log('📡 Message Bus initialized');
  }

  /**
   * 注册代理
   */
  registerAgent(agent: BaseAgent): void {
    const agentId = agent.getStatus().agentId;
    
    if (this.agents.has(agentId)) {
      throw new Error(`Agent ${agentId} is already registered`);
    }

    this.agents.set(agentId, agent);
    
    // 监听代理发送的消息
    agent.on('message', (message: AgentMessage) => {
      this.routeMessage(message);
    });

    console.log(`📝 Agent registered: ${agent.getStatus().displayName} (${agentId})`);
  }

  /**
   * 注销代理
   */
  unregisterAgent(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.removeAllListeners('message');
      this.agents.delete(agentId);
      console.log(`📝 Agent unregistered: ${agentId}`);
    }
  }

  /**
   * 路由消息
   */
  private async routeMessage(message: AgentMessage): Promise<void> {
    // 记录消息历史
    this.addToHistory(message);

    // 广播消息
    if (message.to === 'broadcast') {
      await this.broadcastMessage(message);
      return;
    }

    // 点对点消息
    const targetAgent = this.findAgent(message.to);
    if (!targetAgent) {
      console.error(`❌ Target agent not found: ${message.to}`);
      await this.sendErrorResponse(message, `Target agent not found: ${message.to}`);
      return;
    }

    try {
      const response = await targetAgent.handleMessage(message);
      if (response) {
        // 如果有响应，继续路由
        await this.routeMessage(response);
      }
    } catch (error) {
      console.error(`❌ Error routing message to ${message.to}:`, error);
      await this.sendErrorResponse(message, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 广播消息
   */
  private async broadcastMessage(message: AgentMessage): Promise<void> {
    const senderAgent = this.agents.get(message.from);
    
    for (const [agentId, agent] of this.agents) {
      // 不发送给自己
      if (agentId === message.from) {
        continue;
      }

      try {
        const targetMessage: AgentMessage = {
          ...message,
          to: agentId
        };
        
        await agent.handleMessage(targetMessage);
      } catch (error) {
        console.error(`❌ Error broadcasting to ${agentId}:`, error);
      }
    }
  }

  /**
   * 发送错误响应
   */
  private async sendErrorResponse(originalMessage: AgentMessage, error: string): Promise<void> {
    const errorMessage: AgentMessage = {
      id: `error-${Date.now()}`,
      type: 'error',
      from: 'message-bus',
      to: originalMessage.from,
      payload: {
        error,
        originalMessage
      },
      timestamp: new Date().toISOString(),
      correlationId: originalMessage.correlationId
    };

    const senderAgent = this.agents.get(originalMessage.from);
    if (senderAgent) {
      try {
        await senderAgent.handleMessage(errorMessage);
      } catch (err) {
        console.error(`❌ Error sending error response:`, err);
      }
    }
  }

  /**
   * 查找代理
   */
  private findAgent(identifier: string): BaseAgent | undefined {
    // 首先按 agentId 查找
    if (this.agents.has(identifier)) {
      return this.agents.get(identifier);
    }

    // 然后按 agentType 查找
    for (const agent of this.agents.values()) {
      if (agent.getStatus().agentType === identifier) {
        return agent;
      }
    }

    return undefined;
  }

  /**
   * 添加到消息历史
   */
  private addToHistory(message: AgentMessage): void {
    this.messageHistory.push(message);
    
    // 限制历史记录大小
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 发送消息（外部接口）
   */
  async sendMessage(message: Omit<AgentMessage, 'timestamp'>): Promise<void> {
    const fullMessage: AgentMessage = {
      ...message,
      timestamp: new Date().toISOString()
    };

    await this.routeMessage(fullMessage);
  }

  /**
   * 获取所有代理状态
   */
  getAgentsStatus(): Array<{
    agentId: string;
    agentType: AgentType;
    isActive: boolean;
    currentTask: string | null;
    displayName: string;
  }> {
    return Array.from(this.agents.values()).map(agent => agent.getStatus());
  }

  /**
   * 获取消息历史
   */
  getMessageHistory(limit?: number): AgentMessage[] {
    if (limit) {
      return this.messageHistory.slice(-limit);
    }
    return [...this.messageHistory];
  }

  /**
   * 清理消息历史
   */
  clearHistory(): void {
    this.messageHistory = [];
    console.log('🧹 Message history cleared');
  }

  /**
   * 获取统计信息
   */
  getStatistics(): {
    totalAgents: number;
    activeAgents: number;
    totalMessages: number;
    messagesByType: { [type: string]: number };
    messagesByAgent: { [agentId: string]: number };
  } {
    const activeAgents = Array.from(this.agents.values()).filter(agent => agent.getStatus().isActive).length;
    
    const messagesByType: { [type: string]: number } = {};
    const messagesByAgent: { [agentId: string]: number } = {};

    for (const message of this.messageHistory) {
      messagesByType[message.type] = (messagesByType[message.type] || 0) + 1;
      messagesByAgent[message.from] = (messagesByAgent[message.from] || 0) + 1;
    }

    return {
      totalAgents: this.agents.size,
      activeAgents,
      totalMessages: this.messageHistory.length,
      messagesByType,
      messagesByAgent
    };
  }

  /**
   * 关闭消息总线
   */
  async shutdown(): Promise<void> {
    console.log('🔌 Shutting down Message Bus...');
    
    // 停止所有代理
    for (const agent of this.agents.values()) {
      try {
        await agent.stop();
      } catch (error) {
        console.error(`❌ Error stopping agent:`, error);
      }
    }

    // 清理
    this.agents.clear();
    this.messageHistory = [];
    this.removeAllListeners();
    
    console.log('✅ Message Bus shutdown complete');
  }
}
