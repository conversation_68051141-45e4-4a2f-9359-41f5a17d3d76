/**
 * Spec Agent - 规格代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import { 
  AgentMessage, 
  AgentContext, 
  RequirementsState,
  UserStory,
  AcceptanceCriteria
} from '../types.js';

export class SpecAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('spec', context);
  }

  protected async onStart(): Promise<void> {
    console.log('📋 Spec Agent ready to analyze requirements');
  }

  protected async onStop(): Promise<void> {
    console.log('📋 Spec Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'generate-requirements':
        return await this.generateRequirements(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async generateRequirements(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Analyzing and generating requirements');
    
    try {
      console.log(`📋 Generating requirements for: ${data.title}`);
      
      // 模拟需求分析过程
      const userStories = await this.generateUserStories(data);
      const acceptanceCriteria = await this.generateAcceptanceCriteria(userStories);
      const qualityScore = await this.calculateRequirementsQuality(userStories, acceptanceCriteria);

      const requirementsState: RequirementsState = {
        specName: this.generateSpecName(data.title),
        title: data.title,
        description: data.description,
        userStories,
        acceptanceCriteria,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ requirements: requirementsState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('requirements', qualityScore);

      console.log(`📋 Requirements generated with quality score: ${qualityScore}%`);

      return {
        id: `req-response-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'requirements',
          success: qualityPassed,
          result: requirementsState,
          qualityScore,
          message: qualityPassed ? 'Requirements generated successfully' : 'Requirements quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error generating requirements:', error);
      
      return {
        id: `req-error-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'requirements',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  private async generateUserStories(data: any): Promise<UserStory[]> {
    console.log('📝 Generating user stories...');
    
    // 基于描述生成用户故事
    const stories: UserStory[] = [];
    
    if (data.title.toLowerCase().includes('认证') || data.title.toLowerCase().includes('auth')) {
      stories.push(
        {
          id: 'US001',
          title: '用户注册',
          description: 'As a new user, I want to register an account so that I can access the system',
          acceptanceCriteria: [
            'User can enter email and password',
            'System validates email format',
            'Password meets security requirements',
            'User receives confirmation email'
          ],
          priority: 'high',
          estimatedHours: 8
        },
        {
          id: 'US002', 
          title: '用户登录',
          description: 'As a registered user, I want to login so that I can access my account',
          acceptanceCriteria: [
            'User can enter credentials',
            'System validates credentials',
            'User is redirected to dashboard on success',
            'Error message shown on failure'
          ],
          priority: 'high',
          estimatedHours: 6
        },
        {
          id: 'US003',
          title: '密码重置',
          description: 'As a user, I want to reset my password so that I can regain access if I forget it',
          acceptanceCriteria: [
            'User can request password reset',
            'Reset link sent to email',
            'User can set new password',
            'Old password is invalidated'
          ],
          priority: 'medium',
          estimatedHours: 4
        }
      );
    } else {
      // 通用用户故事生成
      stories.push({
        id: 'US001',
        title: `${data.title} - 核心功能`,
        description: `As a user, I want to use ${data.title} so that I can ${data.description}`,
        acceptanceCriteria: [
          'Core functionality is accessible',
          'User interface is intuitive',
          'System responds within acceptable time',
          'Error handling is graceful'
        ],
        priority: 'high',
        estimatedHours: 12
      });
    }

    return stories;
  }

  private async generateAcceptanceCriteria(userStories: UserStory[]): Promise<AcceptanceCriteria[]> {
    console.log('✅ Generating acceptance criteria...');
    
    const criteria: AcceptanceCriteria[] = [];
    
    userStories.forEach((story, index) => {
      story.acceptanceCriteria.forEach((criterion, criterionIndex) => {
        criteria.push({
          id: `AC${String(index + 1).padStart(3, '0')}-${criterionIndex + 1}`,
          description: criterion,
          testable: true,
          priority: story.priority === 'high' ? 'must' : story.priority === 'medium' ? 'should' : 'could'
        });
      });
    });

    return criteria;
  }

  private async calculateRequirementsQuality(userStories: UserStory[], acceptanceCriteria: AcceptanceCriteria[]): Promise<number> {
    let score = 0;
    
    // 用户故事质量评分
    const storyScore = Math.min(100, userStories.length * 30); // 每个故事30分，最多100分
    score += storyScore * 0.4; // 40%权重

    // 验收标准质量评分
    const criteriaScore = Math.min(100, acceptanceCriteria.length * 10); // 每个标准10分，最多100分
    score += criteriaScore * 0.3; // 30%权重

    // 完整性评分
    const completenessScore = userStories.every(story => 
      story.acceptanceCriteria.length >= 3 && 
      story.estimatedHours > 0
    ) ? 100 : 70;
    score += completenessScore * 0.2; // 20%权重

    // 可测试性评分
    const testabilityScore = acceptanceCriteria.every(criterion => criterion.testable) ? 100 : 80;
    score += testabilityScore * 0.1; // 10%权重

    return Math.round(score);
  }

  private generateSpecName(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.generateRequirements(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'generate-requirements', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'Requirements analysis and specification',
      'User story generation (EARS format)',
      'Acceptance criteria definition',
      'Requirements quality assessment',
      'Stakeholder needs analysis',
      'Functional and non-functional requirements'
    ];
  }
}
