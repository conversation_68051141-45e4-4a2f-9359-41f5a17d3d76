/**
 * Developer Agent - 开发代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import { 
  AgentMessage, 
  AgentContext, 
  ImplementationState,
  GeneratedFile,
  CodeQuality
} from '../types.js';

export class DeveloperAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('developer', context);
  }

  protected async onStart(): Promise<void> {
    console.log('💻 Developer Agent ready to implement code');
  }

  protected async onStop(): Promise<void> {
    console.log('💻 Developer Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'implement-code':
        return await this.implementCode(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async implementCode(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Implementing code based on architecture');
    
    try {
      console.log(`💻 Implementing code for: ${data.requirements?.title}`);
      
      const generatedFiles = await this.generateCode(data);
      const codeQuality = await this.assessCodeQuality(generatedFiles);
      const qualityScore = await this.calculateImplementationQuality(generatedFiles, codeQuality);

      const implementationState: ImplementationState = {
        generatedFiles,
        codeQuality,
        refactoringSuggestions: [],
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ implementation: implementationState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('implementation', qualityScore);

      console.log(`💻 Code implemented with quality score: ${qualityScore}%`);

      return {
        id: `dev-response-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'implementation',
          success: qualityPassed,
          result: implementationState,
          qualityScore,
          message: qualityPassed ? 'Code implemented successfully' : 'Code quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error implementing code:', error);
      
      return {
        id: `dev-error-${Date.now()}`,
        type: 'response',
        to: originalMessage.from,
        payload: {
          phase: 'implementation',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  private async generateCode(data: any): Promise<GeneratedFile[]> {
    console.log('⚡ Generating code files...');
    
    const files: GeneratedFile[] = [];
    const isAuthSystem = data.requirements?.title?.toLowerCase().includes('认证') || 
                        data.requirements?.title?.toLowerCase().includes('auth');

    if (isAuthSystem) {
      // 生成认证系统代码
      files.push(
        {
          path: 'src/models/User.ts',
          content: `/**
 * User model
 */
export interface User {
  id: string;
  email: string;
  password: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserRequest {
  email: string;
  password: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}`,
          type: 'source',
          language: 'typescript',
          qualityScore: 95
        },
        {
          path: 'src/services/AuthService.ts',
          content: `/**
 * Authentication service
 */
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { User, CreateUserRequest, LoginRequest } from '../models/User.js';

export class AuthService {
  private users: User[] = [];

  async register(request: CreateUserRequest): Promise<User> {
    const hashedPassword = await bcrypt.hash(request.password, 10);
    
    const user: User = {
      id: crypto.randomUUID(),
      email: request.email,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.users.push(user);
    return user;
  }

  async login(request: LoginRequest): Promise<string> {
    const user = this.users.find(u => u.email === request.email);
    if (!user) {
      throw new Error('User not found');
    }

    const isValid = await bcrypt.compare(request.password, user.password);
    if (!isValid) {
      throw new Error('Invalid password');
    }

    return jwt.sign({ userId: user.id }, 'secret', { expiresIn: '1h' });
  }
}`,
          type: 'source',
          language: 'typescript',
          qualityScore: 92
        },
        {
          path: 'src/controllers/AuthController.ts',
          content: `/**
 * Authentication controller
 */
import { Request, Response } from 'express';
import { AuthService } from '../services/AuthService.js';

export class AuthController {
  private authService = new AuthService();

  async register(req: Request, res: Response): Promise<void> {
    try {
      const user = await this.authService.register(req.body);
      res.status(201).json({ 
        message: 'User created successfully',
        userId: user.id 
      });
    } catch (error) {
      res.status(400).json({ 
        error: error instanceof Error ? error.message : 'Registration failed' 
      });
    }
  }

  async login(req: Request, res: Response): Promise<void> {
    try {
      const token = await this.authService.login(req.body);
      res.json({ token });
    } catch (error) {
      res.status(401).json({ 
        error: error instanceof Error ? error.message : 'Login failed' 
      });
    }
  }
}`,
          type: 'source',
          language: 'typescript',
          qualityScore: 90
        }
      );
    } else {
      // 生成通用代码
      files.push({
        path: 'src/app.ts',
        content: `/**
 * Main application
 */
import express from 'express';

const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});

export default app;`,
        type: 'source',
        language: 'typescript',
        qualityScore: 88
      });
    }

    return files;
  }

  private async assessCodeQuality(files: GeneratedFile[]): Promise<CodeQuality> {
    console.log('📊 Assessing code quality...');
    
    // 模拟代码质量评估
    const avgQuality = files.reduce((sum, file) => sum + file.qualityScore, 0) / files.length;
    
    return {
      readability: Math.min(100, avgQuality + 5),
      maintainability: avgQuality,
      performance: Math.min(100, avgQuality - 5),
      security: Math.min(100, avgQuality + 3),
      testability: Math.min(100, avgQuality - 2),
      overallGrade: avgQuality >= 95 ? 'A+' : avgQuality >= 90 ? 'A' : avgQuality >= 85 ? 'B+' : 'B'
    };
  }

  private async calculateImplementationQuality(files: GeneratedFile[], codeQuality: CodeQuality): Promise<number> {
    let score = 0;
    
    // 文件数量和质量评分
    const fileScore = Math.min(100, files.length * 25); // 每个文件25分，最多100分
    score += fileScore * 0.3; // 30%权重

    // 代码质量评分
    const qualityAvg = (codeQuality.readability + codeQuality.maintainability + 
                       codeQuality.performance + codeQuality.security + codeQuality.testability) / 5;
    score += qualityAvg * 0.5; // 50%权重

    // 类型安全评分
    const typeScore = files.every(file => file.language === 'typescript') ? 100 : 80;
    score += typeScore * 0.2; // 20%权重

    return Math.round(score);
  }

  async executeTask(taskData: any): Promise<any> {
    return await this.implementCode(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'implement-code', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'Intelligent code generation',
      'TypeScript/JavaScript implementation',
      'Code quality assessment',
      'Best practices enforcement',
      'Modular architecture implementation',
      'Error handling and validation'
    ];
  }
}
