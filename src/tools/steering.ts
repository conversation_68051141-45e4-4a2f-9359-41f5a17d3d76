/**
 * Steering system MCP tools
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import { ensureWorkflowDirectories } from "../utils.js";


/**
 * Register steering system tools
 */
export function registerSteeringTools(server: McpServer): void {
  // Initialize steering system
  server.tool(
    "init-steering",
    "Initialize Steering document system, create product overview, tech stack and project structure documents",
    {
      rootPath: z.string().describe("Project root directory path"),
    },
    async ({ rootPath }) => {
      try {
        await ensureWorkflowDirectories(rootPath);

        const steeringDir = path.join(rootPath, ".vibecode", "steering");

        // Create product overview document
        const productTemplate = `# Product Overview

## Product Vision
> Describe the core vision and goals of the product

## Target Users
> Define the main user groups and their needs

## Core Features
> List the main functional features of the product

## Success Metrics
> Define the criteria for measuring product success

---
*This document was automatically generated by vibe-coding workflow, please update according to actual project conditions*
`;

        const techTemplate = `# Technology Stack

## Development Languages
- TypeScript
- Node.js

## Frameworks and Libraries
- @modelcontextprotocol/sdk
- Zod

## Development Tools
- pnpm
- TypeScript

## Technical Constraints
> Describe constraints and requirements for technology selection

## Third-party Integrations
> List third-party services and APIs used

---
*This document was automatically generated by vibe-coding workflow, please update according to actual project conditions*
`;

        const structureTemplate = `# Project Structure

## File Organization Pattern
\`\`\`
.vibecode/
├── steering/           # Steering documents
├── workflows/          # Workflow processes
├── templates/          # Template files
└── *.md               # Project documents
\`\`\`

## Naming Conventions
- File names: kebab-case
- Variable names: camelCase
- Class names: PascalCase

## Import Patterns
- Use relative paths for importing local modules
- Organize import statements alphabetically

## Code Organization Principles
- Single Responsibility Principle
- Modular design
- Clear interface definitions

---
*This document was automatically generated by vibe-coding workflow, please update according to actual project conditions*
`;

        // Write files
        await fs.writeFile(path.join(steeringDir, "product.md"), productTemplate, "utf-8");
        await fs.writeFile(path.join(steeringDir, "tech.md"), techTemplate, "utf-8");
        await fs.writeFile(path.join(steeringDir, "structure.md"), structureTemplate, "utf-8");

        return {
          content: [
            {
              type: "text",
              text: "✅ Steering document system initialized successfully!\n\nCreated documents:\n- .vibecode/steering/product.md - Product overview\n- .vibecode/steering/tech.md - Technology stack\n- .vibecode/steering/structure.md - Project structure\n\nPlease update these document contents according to actual project conditions.",
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to initialize Steering document system: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Get steering documents
  server.tool(
    "get-steering",
    "Get Steering document content for guiding workflow processes",
    {
      rootPath: z.string().describe("Project root directory path"),
      type: z.enum(["all", "product", "tech", "structure"]).optional().describe("Get specific type of steering documents"),
    },
    async ({ rootPath, type = "all" }) => {
      try {
        const steeringDir = path.join(rootPath, ".vibecode", "steering");

        if (!existsSync(steeringDir)) {
          return {
            content: [
              {
                type: "text",
                text: "❌ Steering document directory does not exist, please run init-steering first to initialize.",
              },
            ],
          };
        }

        const files = type === "all"
          ? ["product.md", "tech.md", "structure.md"]
          : [`${type}.md`];

        const contents: string[] = [];

        for (const file of files) {
          const filePath = path.join(steeringDir, file);
          if (existsSync(filePath)) {
            const content = await fs.readFile(filePath, "utf-8");
            const name = path.basename(file, ".md");
            contents.push(`## ${name.toUpperCase()}\n\n${content}\n`);
          }
        }

        return {
          content: [
            {
              type: "text",
              text: contents.length > 0
                ? `📋 Steering document content:\n\n${contents.join("\n---\n\n")}`
                : "❌ No specified Steering documents found.",
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to get Steering documents: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}
