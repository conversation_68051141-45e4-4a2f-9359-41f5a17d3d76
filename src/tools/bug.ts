/**
 * Bug fix workflow MCP tools
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import {
  ensureWorkflowDirectories,
  loadWorkflowConfig,
  saveWorkflowConfig,
  generateSpecName,
  getCurrentTimestamp
} from "../utils.js";
import { BugConfig, SeverityLevel } from "../types.js";

/**
 * Register bug fix workflow tools
 */
export function registerBugTools(server: McpServer): void {
  // Create new bug report
  server.tool(
    "bug-create",
    "Create new bug fix workflow",
    {
      rootPath: z.string().describe("Project root directory path"),
      title: z.string().describe("Bug title"),
      description: z.string().describe("Bug description"),
      severity: z.enum(["low", "medium", "high", "critical"]).optional().describe("Severity level"),
    },
    async ({ rootPath, title, description, severity = "medium" }) => {
      try {
        await ensureWorkflowDirectories(rootPath);

        const bugName = generateSpecName(title);
        const config = await loadWorkflowConfig(rootPath);

        // Check if bug already exists
        if (config.bugs[bugName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Bug "${bugName}" already exists. Please use a different title or delete the existing bug.`,
              },
            ],
          };
        }

        // Create bug configuration
        const bugConfig: BugConfig = {
          name: bugName,
          title,
          status: 'reported',
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp(),
          severity: severity as SeverityLevel,
        };

        config.bugs[bugName] = bugConfig;
        await saveWorkflowConfig(rootPath, config);

        // Create bug directory
        const bugDir = path.join(rootPath, ".vibecode", "workflows", "bugs", bugName);
        await fs.mkdir(bugDir, { recursive: true });

        // Create bug report document
        const severityEmoji = {
          'low': '🟢',
          'medium': '🟡',
          'high': '🟠',
          'critical': '🔴'
        };

        const reportContent = `# ${title}

## Bug Information
- **Severity**: ${severityEmoji[severity as SeverityLevel]} ${severity}
- **Status**: ${bugConfig.status}
- **Created at**: ${bugConfig.createdAt}
- **Last updated**: ${bugConfig.updatedAt}

## Problem Description
${description}

## Reproduction Steps
1. [Step 1]
2. [Step 2]
3. [Step 3]

## Expected Behavior
[Describe the expected correct behavior]

## Actual Behavior
[Describe the actual error behavior]

## Environment Information
- **Operating System**:
- **Browser/Node Version**:
- **Project Version**:

## Error Information
\`\`\`
[Paste error logs or stack trace]
\`\`\`

## Impact Scope
- [ ] Affects core functionality
- [ ] Affects user experience
- [ ] Affects performance
- [ ] Security issue

## Workflow
1. ✅ Create report (completed)
2. ⏳ Problem analysis (pending) - Use \`bug-analyze\`
3. ⏳ Fix implementation (pending) - Use \`bug-fix\`
4. ⏳ Verification testing (pending) - Use \`bug-verify\`

## Next Step
Run \`bug-analyze\` to start the problem analysis phase.

---
*Generated at: ${getCurrentTimestamp()}*
`;

        await fs.writeFile(path.join(bugDir, "report.md"), reportContent, "utf-8");

        return {
          content: [
            {
              type: "text",
              text: `✅ Bug "${title}" created successfully!\n\n${severityEmoji[severity as SeverityLevel]} Severity: ${severity}\nBug name: ${bugName}\nStatus: ${bugConfig.status}\n\nNext step: Run \`bug-analyze\` to start the problem analysis phase.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to create bug: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // View bug status
  server.tool(
    "bug-status",
    "View current status of bugs",
    {
      rootPath: z.string().describe("Project root directory path"),
      bugName: z.string().optional().describe("Bug name, if not provided shows all bugs"),
    },
    async ({ rootPath, bugName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (Object.keys(config.bugs).length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "🐛 No bugs currently exist. Use `bug-create` to create new bug reports.",
              },
            ],
          };
        }

        if (bugName) {
          // Show specific bug status
          if (!config.bugs[bugName]) {
            return {
              content: [
                {
                  type: "text",
                  text: `❌ Bug "${bugName}" does not exist.`,
                },
              ],
            };
          }

          const bug = config.bugs[bugName];
          const statusEmoji = {
            'reported': '📋',
            'analyzing': '🔍',
            'fixing': '🔧',
            'verifying': '✅',
            'resolved': '✅'
          };

          const severityEmoji = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'critical': '🔴'
          };

          return {
            content: [
              {
                type: "text",
                text: `🐛 Bug status: ${bug.title}\n\n${statusEmoji[bug.status]} Current status: ${bug.status}\n${severityEmoji[bug.severity]} Severity: ${bug.severity}\n📅 Created at: ${bug.createdAt}\n🔄 Last updated: ${bug.updatedAt}`,
              },
            ],
          };
        } else {
          // Show all bugs status
          const bugList = Object.values(config.bugs).map(bug => {
            const statusEmoji = {
              'reported': '📋',
              'analyzing': '🔍',
              'fixing': '🔧',
              'verifying': '✅',
              'resolved': '✅'
            };

            const severityEmoji = {
              'low': '🟢',
              'medium': '🟡',
              'high': '🟠',
              'critical': '🔴'
            };

            return `${statusEmoji[bug.status]} ${severityEmoji[bug.severity]} ${bug.title} (${bug.name}) - ${bug.status}`;
          }).join('\n');

          return {
            content: [
              {
                type: "text",
                text: `🐛 All bugs status:\n\n${bugList}`,
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to get bug status: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Analyze bug
  server.tool(
    "bug-analyze",
    "Analyze bug and identify root cause",
    {
      rootPath: z.string().describe("Project root directory path"),
      bugName: z.string().describe("Bug name"),
    },
    async ({ rootPath, bugName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.bugs[bugName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Bug "${bugName}" does not exist. Please create it first using bug-create.`,
              },
            ],
          };
        }

        const bugConfig = config.bugs[bugName];
        const bugDir = path.join(rootPath, ".vibecode", "workflows", "bugs", bugName);

        // Check if bug report exists
        const reportPath = path.join(bugDir, "report.md");
        let reportContent = "";
        if (existsSync(reportPath)) {
          reportContent = await fs.readFile(reportPath, "utf-8");
        }

        const analysisTemplate = `# ${bugConfig.title} - Analysis Document

## Bug Report Reference
${reportContent}

## Root Cause Analysis

### Investigation Steps
1. **Reproduce the Issue**
   - [ ] Followed reproduction steps from bug report
   - [ ] Confirmed issue exists in current environment
   - [ ] Documented exact conditions when bug occurs

2. **Code Investigation**
   - [ ] Identified relevant code sections
   - [ ] Reviewed recent changes in affected areas
   - [ ] Checked related components and dependencies

3. **Data Analysis**
   - [ ] Examined logs and error messages
   - [ ] Analyzed database state (if applicable)
   - [ ] Reviewed user input patterns

### Findings

#### Primary Root Cause
**Location**: [File/Module/Function where issue originates]
**Description**: [Detailed explanation of what's causing the bug]
**Evidence**: [Code snippets, logs, or data that support this conclusion]

#### Contributing Factors
1. **Factor 1**: [Description]
2. **Factor 2**: [Description]
3. **Factor 3**: [Description]

#### Impact Assessment
- **Affected Users**: [Number/percentage of users affected]
- **Affected Features**: [List of features impacted]
- **Data Integrity**: [Any data corruption or loss risks]
- **Performance Impact**: [System performance effects]

### Technical Analysis

#### Code Review
\`\`\`[language]
// Current problematic code
[Insert relevant code snippet]
\`\`\`

**Issues Identified**:
- [Issue 1]: [Description]
- [Issue 2]: [Description]

#### System Dependencies
- **External Services**: [Any external dependencies involved]
- **Database**: [Database-related factors]
- **Configuration**: [Configuration issues]
- **Environment**: [Environment-specific factors]

### Fix Strategy

#### Proposed Solution
**Approach**: [High-level approach to fix the issue]
**Changes Required**:
1. [Change 1]: [Description]
2. [Change 2]: [Description]
3. [Change 3]: [Description]

#### Alternative Solutions
1. **Option A**: [Description, pros/cons]
2. **Option B**: [Description, pros/cons]

#### Risk Assessment
- **Implementation Risk**: [Low/Medium/High] - [Explanation]
- **Regression Risk**: [Low/Medium/High] - [Explanation]
- **Deployment Risk**: [Low/Medium/High] - [Explanation]

### Testing Strategy

#### Test Cases
1. **Regression Tests**
   - [ ] Verify fix resolves the reported issue
   - [ ] Ensure no new issues are introduced
   - [ ] Test edge cases and boundary conditions

2. **Integration Tests**
   - [ ] Test interaction with related components
   - [ ] Verify data flow remains correct
   - [ ] Check API contracts are maintained

3. **Performance Tests**
   - [ ] Measure performance impact of fix
   - [ ] Verify no performance degradation
   - [ ] Test under load conditions

### Implementation Plan

#### Phase 1: Preparation
- [ ] Create feature branch for bug fix
- [ ] Set up test environment
- [ ] Prepare test data

#### Phase 2: Implementation
- [ ] Implement the fix
- [ ] Add/update unit tests
- [ ] Update documentation

#### Phase 3: Validation
- [ ] Run comprehensive tests
- [ ] Perform code review
- [ ] Validate fix in staging environment

#### Phase 4: Deployment
- [ ] Deploy to production
- [ ] Monitor for issues
- [ ] Verify fix is working

### Monitoring Plan
- **Metrics to Track**: [List of metrics to monitor post-fix]
- **Alert Conditions**: [When to trigger alerts]
- **Rollback Plan**: [How to rollback if issues arise]

---
*Analysis completed: ${getCurrentTimestamp()}*
*Next step: Run \`bug-fix\` to implement the solution*
`;

        await fs.writeFile(path.join(bugDir, "analysis.md"), analysisTemplate, "utf-8");

        // Update bug status
        bugConfig.status = 'analyzing';
        bugConfig.updatedAt = getCurrentTimestamp();
        config.bugs[bugName] = bugConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Bug analysis document generated successfully!\n\nBug: ${bugConfig.title}\nStatus: ${bugConfig.status}\n\nAnalysis document saved to: .vibecode/workflows/bugs/${bugName}/analysis.md\n\n🔍 **Analysis Guidelines:**\n1. Follow the investigation steps systematically\n2. Document all findings and evidence\n3. Identify root cause and contributing factors\n4. Plan fix strategy and testing approach\n\nNext step: Run \`bug-fix\` to implement the solution.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to generate analysis document: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Fix bug implementation
  server.tool(
    "bug-fix",
    "Implement bug fix based on analysis",
    {
      rootPath: z.string().describe("Project root directory path"),
      bugName: z.string().describe("Bug name"),
    },
    async ({ rootPath, bugName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.bugs[bugName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Bug "${bugName}" does not exist. Please create it first using bug-create.`,
              },
            ],
          };
        }

        const bugConfig = config.bugs[bugName];
        const bugDir = path.join(rootPath, ".vibecode", "workflows", "bugs", bugName);

        // Check if analysis document exists
        const analysisPath = path.join(bugDir, "analysis.md");
        let analysisContent = "";
        if (existsSync(analysisPath)) {
          analysisContent = await fs.readFile(analysisPath, "utf-8");
        }

        const fixTemplate = `# ${bugConfig.title} - Fix Implementation

## Analysis Reference
${analysisContent ? `\n${analysisContent}\n` : "Please complete analysis first using bug-analyze."}

## Implementation Details

### Fix Summary
**Issue**: [Brief description of the bug]
**Solution**: [Brief description of the fix]
**Files Modified**: [List of files changed]

### Code Changes

#### Before (Problematic Code)
\`\`\`[language]
// Original code that caused the issue
[Insert original code snippet]
\`\`\`

#### After (Fixed Code)
\`\`\`[language]
// Fixed code with explanation
[Insert fixed code snippet]
\`\`\`

**Explanation**: [Detailed explanation of what was changed and why]

### Additional Changes

#### New Files Created
- \`[file1]\`: [Purpose]
- \`[file2]\`: [Purpose]

#### Configuration Changes
- \`[config-file]\`: [Changes made]

#### Database Changes
- \`[migration-file]\`: [Schema changes]

### Testing Implementation

#### Unit Tests Added/Updated
\`\`\`[language]
// Test case for the bug fix
[Insert test code]
\`\`\`

#### Integration Tests
- [ ] Test case 1: [Description]
- [ ] Test case 2: [Description]

#### Manual Testing Checklist
- [ ] Reproduce original bug (should be fixed)
- [ ] Test normal functionality (should work)
- [ ] Test edge cases (should handle gracefully)
- [ ] Test error conditions (should fail safely)

### Deployment Notes

#### Pre-deployment Checklist
- [ ] Code review completed
- [ ] All tests passing
- [ ] Documentation updated
- [ ] Staging environment tested

#### Deployment Steps
1. [Step 1]: [Description]
2. [Step 2]: [Description]
3. [Step 3]: [Description]

#### Post-deployment Monitoring
- [ ] Monitor error logs
- [ ] Check performance metrics
- [ ] Verify user reports
- [ ] Monitor related systems

### Risk Mitigation

#### Rollback Plan
**Trigger Conditions**: [When to rollback]
**Rollback Steps**:
1. [Step 1]: [Description]
2. [Step 2]: [Description]

#### Monitoring Alerts
- **Error Rate**: Alert if > [threshold]
- **Performance**: Alert if response time > [threshold]
- **User Impact**: Alert if user complaints > [threshold]

### Documentation Updates

#### User Documentation
- [ ] Updated user manual
- [ ] Updated FAQ
- [ ] Updated troubleshooting guide

#### Developer Documentation
- [ ] Updated API documentation
- [ ] Updated code comments
- [ ] Updated architecture diagrams

---
*Fix implemented: ${getCurrentTimestamp()}*
*Next step: Run \`bug-verify\` to validate the fix*
`;

        await fs.writeFile(path.join(bugDir, "fix.md"), fixTemplate, "utf-8");

        // Update bug status
        bugConfig.status = 'fixing';
        bugConfig.updatedAt = getCurrentTimestamp();
        config.bugs[bugName] = bugConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Bug fix implementation document generated!\n\nBug: ${bugConfig.title}\nStatus: ${bugConfig.status}\n\nFix document saved to: .vibecode/workflows/bugs/${bugName}/fix.md\n\n🔧 **Implementation Guidelines:**\n1. Follow the fix strategy from analysis\n2. Document all code changes\n3. Add comprehensive tests\n4. Update relevant documentation\n5. Plan deployment and monitoring\n\nNext step: Run \`bug-verify\` to validate the fix.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to generate fix document: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Verify bug fix
  server.tool(
    "bug-verify",
    "Verify bug fix and validate solution",
    {
      rootPath: z.string().describe("Project root directory path"),
      bugName: z.string().describe("Bug name"),
    },
    async ({ rootPath, bugName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.bugs[bugName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Bug "${bugName}" does not exist. Please create it first using bug-create.`,
              },
            ],
          };
        }

        const bugConfig = config.bugs[bugName];
        const bugDir = path.join(rootPath, ".vibecode", "workflows", "bugs", bugName);

        // Check if fix document exists
        const fixPath = path.join(bugDir, "fix.md");
        let fixContent = "";
        if (existsSync(fixPath)) {
          fixContent = await fs.readFile(fixPath, "utf-8");
        }

        const verificationTemplate = `# ${bugConfig.title} - Verification Report

## Fix Implementation Reference
${fixContent ? `\n${fixContent}\n` : "Please complete fix implementation first using bug-fix."}

## Verification Results

### Test Execution Summary
**Verification Date**: ${getCurrentTimestamp()}
**Verification Status**: [PASS/FAIL/PARTIAL]
**Overall Result**: [Brief summary]

### Functional Testing

#### Original Bug Reproduction
- [ ] **PASS**: Original bug no longer occurs
- [ ] **FAIL**: Original bug still exists
- **Notes**: [Detailed notes about reproduction test]

#### Core Functionality Testing
- [ ] **PASS**: Main functionality works correctly
- [ ] **FAIL**: Main functionality has issues
- **Notes**: [Detailed notes about functionality test]

#### Edge Case Testing
- [ ] **PASS**: Edge cases handled properly
- [ ] **FAIL**: Edge cases cause issues
- **Notes**: [Detailed notes about edge case testing]

#### Error Handling Testing
- [ ] **PASS**: Error conditions handled gracefully
- [ ] **FAIL**: Error conditions cause problems
- **Notes**: [Detailed notes about error handling]

### Regression Testing

#### Related Features
- [ ] **PASS**: Feature A works correctly
- [ ] **PASS**: Feature B works correctly
- [ ] **PASS**: Feature C works correctly
- **Notes**: [Any issues found in related features]

#### Integration Points
- [ ] **PASS**: API integration working
- [ ] **PASS**: Database integration working
- [ ] **PASS**: External service integration working
- **Notes**: [Integration test results]

### Performance Testing

#### Response Time
- **Before Fix**: [X]ms average
- **After Fix**: [Y]ms average
- **Impact**: [Improvement/Degradation/No Change]

#### Resource Usage
- **Memory**: [Before] → [After]
- **CPU**: [Before] → [After]
- **Database**: [Before] → [After]

#### Load Testing
- [ ] **PASS**: System handles normal load
- [ ] **PASS**: System handles peak load
- **Notes**: [Load testing results]

### Security Testing

#### Security Scan Results
- [ ] **PASS**: No new vulnerabilities introduced
- [ ] **PASS**: Existing vulnerabilities not affected
- **Notes**: [Security scan details]

#### Access Control
- [ ] **PASS**: Authorization still working
- [ ] **PASS**: Authentication still working
- **Notes**: [Access control test results]

### User Acceptance Testing

#### User Feedback
- **Positive Feedback**: [Count/Percentage]
- **Negative Feedback**: [Count/Percentage]
- **Key Comments**: [Summary of user feedback]

#### Usability Testing
- [ ] **PASS**: User interface remains intuitive
- [ ] **PASS**: User workflow not disrupted
- **Notes**: [Usability test results]

### Production Validation

#### Deployment Results
- **Deployment Date**: [Date]
- **Deployment Status**: [Success/Failed/Partial]
- **Issues Encountered**: [List any deployment issues]

#### Monitoring Results
- **Error Rate**: [Before] → [After]
- **User Complaints**: [Before] → [After]
- **System Stability**: [Assessment]

#### Rollback Assessment
- **Rollback Needed**: [Yes/No]
- **Reason**: [If rollback needed, explain why]
- **Rollback Status**: [If applicable]

### Final Assessment

#### Resolution Status
- [ ] **RESOLVED**: Bug completely fixed
- [ ] **PARTIALLY RESOLVED**: Bug mostly fixed with minor issues
- [ ] **NOT RESOLVED**: Bug still exists
- [ ] **NEW ISSUES**: Fix introduced new problems

#### Lessons Learned
1. [Lesson 1]: [Description]
2. [Lesson 2]: [Description]
3. [Lesson 3]: [Description]

#### Recommendations
1. [Recommendation 1]: [Description]
2. [Recommendation 2]: [Description]
3. [Recommendation 3]: [Description]

### Sign-off

#### Technical Sign-off
- **Developer**: [Name] - [Date]
- **Code Reviewer**: [Name] - [Date]
- **QA Engineer**: [Name] - [Date]

#### Business Sign-off
- **Product Owner**: [Name] - [Date]
- **Stakeholder**: [Name] - [Date]

---
*Verification completed: ${getCurrentTimestamp()}*
*Bug resolution workflow complete*
`;

        await fs.writeFile(path.join(bugDir, "verification.md"), verificationTemplate, "utf-8");

        // Update bug status
        bugConfig.status = 'verifying';
        bugConfig.updatedAt = getCurrentTimestamp();
        config.bugs[bugName] = bugConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Bug verification document generated!\n\nBug: ${bugConfig.title}\nStatus: ${bugConfig.status}\n\nVerification document saved to: .vibecode/workflows/bugs/${bugName}/verification.md\n\n✅ **Verification Guidelines:**\n1. Execute all test cases systematically\n2. Validate fix resolves original issue\n3. Check for regression issues\n4. Monitor production deployment\n5. Gather user feedback\n6. Complete sign-off process\n\n🎉 **Bug Fix Workflow Complete!**\nOnce verification is successful, update bug status to 'resolved'.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to generate verification document: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}
