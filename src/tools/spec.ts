/**
 * Specification workflow MCP tools
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { z } from "zod";
import {
  ensureWorkflowDirectories,
  loadWorkflowConfig,
  saveWorkflowConfig,
  generateSpecName,
  getCurrentTimestamp
} from "../utils.js";
import { SpecConfig } from "../types.js";

/**
 * Register specification workflow tools
 */
export function registerSpecTools(server: McpServer): void {
  // Create new specification
  server.tool(
    "spec-create",
    "Create new specification workflow",
    {
      rootPath: z.string().describe("Project root directory path"),
      title: z.string().describe("Specification title"),
      description: z.string().optional().describe("Specification description"),
    },
    async ({ rootPath, title, description = "" }) => {
      try {
        await ensureWorkflowDirectories(rootPath);

        const specName = generateSpecName(title);
        const config = await loadWorkflowConfig(rootPath);

        // Check if specification already exists
        if (config.specs[specName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${specName}" already exists. Please use a different title or delete the existing specification.`,
              },
            ],
          };
        }

        // Create specification configuration
        const specConfig: SpecConfig = {
          name: specName,
          title,
          status: 'created',
          createdAt: getCurrentTimestamp(),
          updatedAt: getCurrentTimestamp(),
        };

        config.specs[specName] = specConfig;
        await saveWorkflowConfig(rootPath, config);

        // Create specification directory
        const specDir = path.join(rootPath, ".vibecode", "workflows", "specs", specName);
        await fs.mkdir(specDir, { recursive: true });

        // Create initial document
        const overviewContent = `# ${title}

## Overview
${description || "Please add specification description"}

## Status
- Current status: ${specConfig.status}
- Created at: ${specConfig.createdAt}
- Last updated: ${specConfig.updatedAt}

## Workflow
1. ✅ Create specification (completed)
2. ⏳ Requirements analysis (pending) - Use \`spec-requirements\`
3. ⏳ Design document (pending) - Use \`spec-design\`
4. ⏳ Task breakdown (pending) - Use \`spec-tasks\`
5. ⏳ Implementation (pending) - Use \`spec-execute\`

## Next Step
Run \`spec-requirements\` to start the requirements analysis phase.
`;

        await fs.writeFile(path.join(specDir, "overview.md"), overviewContent, "utf-8");

        return {
          content: [
            {
              type: "text",
              text: `✅ Specification "${title}" created successfully!\n\nSpecification name: ${specName}\nStatus: ${specConfig.status}\n\nNext step: Run \`spec-requirements\` to start the requirements analysis phase.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to create specification: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Generate requirements document
  server.tool(
    "spec-requirements",
    "Generate requirements document for current specification",
    {
      rootPath: z.string().describe("Project root directory path"),
      specName: z.string().describe("Specification name"),
    },
    async ({ rootPath, specName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.specs[specName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${specName}" does not exist. Please create it first using spec-create.`,
              },
            ],
          };
        }

        const specConfig = config.specs[specName];
        const specDir = path.join(rootPath, ".vibecode", "workflows", "specs", specName);

        // Get Steering document content as context
        const steeringDir = path.join(rootPath, ".vibecode", "steering");
        let steeringContext = "";

        if (existsSync(steeringDir)) {
          const steeringFiles = ["product.md", "tech.md", "structure.md"];
          for (const file of steeringFiles) {
            const filePath = path.join(steeringDir, file);
            if (existsSync(filePath)) {
              const content = await fs.readFile(filePath, "utf-8");
              steeringContext += `\n## ${file.replace('.md', '').toUpperCase()}\n${content}\n`;
            }
          }
        }

        const requirementsTemplate = `# ${specConfig.title} - Requirements Document

## Project Context
${steeringContext}

## User Stories

### Main User Stories
> Use EARS format (WHEN/IF/THEN) to describe user requirements

**User Story 1:**
- WHEN: [Trigger condition]
- IF: [Precondition]
- THEN: [Expected result]

**User Story 2:**
- WHEN: [Trigger condition]
- IF: [Precondition]
- THEN: [Expected result]

## Functional Requirements

### Core Features
1. [Feature 1 description]
2. [Feature 2 description]
3. [Feature 3 description]

### Secondary Features
1. [Secondary feature 1]
2. [Secondary feature 2]

## Non-functional Requirements

### Performance Requirements
- [Performance metrics]

### Security Requirements
- [Security standards]

### Usability Requirements
- [Usability standards]

## Acceptance Criteria

### Functional Acceptance
- [ ] [Acceptance condition 1]
- [ ] [Acceptance condition 2]
- [ ] [Acceptance condition 3]

### Quality Acceptance
- [ ] [Quality standard 1]
- [ ] [Quality standard 2]

## Constraints
- [Technical constraints]
- [Time constraints]
- [Resource constraints]

---
*Generated at: ${getCurrentTimestamp()}*
*Please update this document according to actual requirements*
`;

        await fs.writeFile(path.join(specDir, "requirements.md"), requirementsTemplate, "utf-8");

        // Update specification status
        specConfig.status = 'requirements';
        specConfig.updatedAt = getCurrentTimestamp();
        config.specs[specName] = specConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Requirements document generated successfully!\n\nSpecification: ${specConfig.title}\nStatus: ${specConfig.status}\n\nRequirements document saved to: .vibecode/workflows/specs/${specName}/requirements.md\n\nNext step: Run \`spec-design\` to start the design phase.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to generate requirements document: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Generate design document
  server.tool(
    "spec-design",
    "Generate design document for current specification",
    {
      rootPath: z.string().describe("Project root directory path"),
      specName: z.string().describe("Specification name"),
    },
    async ({ rootPath, specName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.specs[specName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${specName}" does not exist. Please create it first using spec-create.`,
              },
            ],
          };
        }

        const specConfig = config.specs[specName];
        const specDir = path.join(rootPath, ".vibecode", "workflows", "specs", specName);

        // Check if requirements document exists
        const requirementsPath = path.join(specDir, "requirements.md");
        let requirementsContent = "";
        if (existsSync(requirementsPath)) {
          requirementsContent = await fs.readFile(requirementsPath, "utf-8");
        }

        // Get Steering document content as context
        const steeringDir = path.join(rootPath, ".vibecode", "steering");
        let steeringContext = "";

        if (existsSync(steeringDir)) {
          const steeringFiles = ["product.md", "tech.md", "structure.md"];
          for (const file of steeringFiles) {
            const filePath = path.join(steeringDir, file);
            if (existsSync(filePath)) {
              const content = await fs.readFile(filePath, "utf-8");
              steeringContext += `\n## ${file.replace('.md', '').toUpperCase()}\n${content}\n`;
            }
          }
        }

        const designTemplate = `# ${specConfig.title} - Design Document

## Project Context
${steeringContext}

## Requirements Reference
${requirementsContent ? `\n${requirementsContent}\n` : "Please complete requirements document first using spec-requirements."}

## System Architecture

### High-Level Architecture
\`\`\`mermaid
graph TB
    A[User Interface] --> B[Business Logic]
    B --> C[Data Layer]
    C --> D[Database]
\`\`\`

### Component Design
1. **Frontend Components**
   - [Component 1]: [Description]
   - [Component 2]: [Description]

2. **Backend Services**
   - [Service 1]: [Description]
   - [Service 2]: [Description]

3. **Data Models**
   - [Model 1]: [Description]
   - [Model 2]: [Description]

## API Design

### REST Endpoints
\`\`\`
GET    /api/[resource]           # List resources
POST   /api/[resource]           # Create resource
GET    /api/[resource]/:id       # Get specific resource
PUT    /api/[resource]/:id       # Update resource
DELETE /api/[resource]/:id       # Delete resource
\`\`\`

### Request/Response Schemas
\`\`\`typescript
interface [ResourceRequest] {
  // Define request structure
}

interface [ResourceResponse] {
  // Define response structure
}
\`\`\`

## Database Design

### Entity Relationship Diagram
\`\`\`mermaid
erDiagram
    USER ||--o{ ORDER : places
    ORDER ||--|{ ORDER_ITEM : contains
    PRODUCT ||--o{ ORDER_ITEM : "ordered in"
\`\`\`

### Table Schemas
\`\`\`sql
CREATE TABLE [table_name] (
  id SERIAL PRIMARY KEY,
  -- Define columns
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

## Security Design

### Authentication & Authorization
- [Authentication method]
- [Authorization strategy]
- [Security measures]

### Data Protection
- [Encryption methods]
- [Data validation]
- [Privacy considerations]

## Performance Considerations

### Scalability
- [Horizontal scaling strategy]
- [Vertical scaling considerations]
- [Load balancing approach]

### Optimization
- [Caching strategy]
- [Database optimization]
- [Frontend optimization]

## Technology Stack

### Frontend
- Framework: [Framework name]
- State Management: [State management solution]
- UI Library: [UI library]

### Backend
- Runtime: [Runtime environment]
- Framework: [Backend framework]
- Database: [Database system]

### DevOps
- Deployment: [Deployment strategy]
- Monitoring: [Monitoring tools]
- CI/CD: [CI/CD pipeline]

## Implementation Plan

### Phase 1: Foundation
- [ ] Set up project structure
- [ ] Configure development environment
- [ ] Implement basic authentication

### Phase 2: Core Features
- [ ] Implement main functionality
- [ ] Add data persistence
- [ ] Create user interface

### Phase 3: Enhancement
- [ ] Add advanced features
- [ ] Optimize performance
- [ ] Implement monitoring

## Risk Assessment

### Technical Risks
- [Risk 1]: [Mitigation strategy]
- [Risk 2]: [Mitigation strategy]

### Business Risks
- [Risk 1]: [Mitigation strategy]
- [Risk 2]: [Mitigation strategy]

---
*Generated at: ${getCurrentTimestamp()}*
*Please update this document according to actual design requirements*
`;

        await fs.writeFile(path.join(specDir, "design.md"), designTemplate, "utf-8");

        // Update specification status
        specConfig.status = 'design';
        specConfig.updatedAt = getCurrentTimestamp();
        config.specs[specName] = specConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Design document generated successfully!\n\nSpecification: ${specConfig.title}\nStatus: ${specConfig.status}\n\nDesign document saved to: .vibecode/workflows/specs/${specName}/design.md\n\nNext step: Run \`spec-tasks\` to start the task breakdown phase.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to generate design document: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Generate tasks breakdown
  server.tool(
    "spec-tasks",
    "Generate task breakdown for current specification",
    {
      rootPath: z.string().describe("Project root directory path"),
      specName: z.string().describe("Specification name"),
    },
    async ({ rootPath, specName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.specs[specName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${specName}" does not exist. Please create it first using spec-create.`,
              },
            ],
          };
        }

        const specConfig = config.specs[specName];
        const specDir = path.join(rootPath, ".vibecode", "workflows", "specs", specName);

        // Check if design document exists
        const designPath = path.join(specDir, "design.md");
        let designContent = "";
        if (existsSync(designPath)) {
          designContent = await fs.readFile(designPath, "utf-8");
        }

        const tasksTemplate = `# ${specConfig.title} - Task Breakdown

## Design Reference
${designContent ? `\n${designContent}\n` : "Please complete design document first using spec-design."}

## Development Tasks

### Phase 1: Project Setup
#### Task 1.1: Environment Setup
- **Description**: Set up development environment and project structure
- **Acceptance Criteria**:
  - [ ] Development environment configured
  - [ ] Project dependencies installed
  - [ ] Build system working
- **Estimated Time**: 2-4 hours
- **Dependencies**: None
- **Assignee**: [Developer name]

#### Task 1.2: Database Setup
- **Description**: Set up database schema and initial configuration
- **Acceptance Criteria**:
  - [ ] Database schema created
  - [ ] Migration scripts written
  - [ ] Test data populated
- **Estimated Time**: 3-5 hours
- **Dependencies**: Task 1.1
- **Assignee**: [Developer name]

### Phase 2: Backend Development
#### Task 2.1: API Foundation
- **Description**: Implement basic API structure and authentication
- **Acceptance Criteria**:
  - [ ] API routes defined
  - [ ] Authentication middleware implemented
  - [ ] Error handling configured
- **Estimated Time**: 4-6 hours
- **Dependencies**: Task 1.2
- **Assignee**: [Developer name]

#### Task 2.2: Core Business Logic
- **Description**: Implement main business logic and data operations
- **Acceptance Criteria**:
  - [ ] Core models implemented
  - [ ] Business rules enforced
  - [ ] Data validation added
- **Estimated Time**: 8-12 hours
- **Dependencies**: Task 2.1
- **Assignee**: [Developer name]

### Phase 3: Frontend Development
#### Task 3.1: UI Components
- **Description**: Create reusable UI components
- **Acceptance Criteria**:
  - [ ] Component library created
  - [ ] Styling system implemented
  - [ ] Component tests written
- **Estimated Time**: 6-8 hours
- **Dependencies**: Task 2.1
- **Assignee**: [Developer name]

#### Task 3.2: User Interface
- **Description**: Implement main user interface screens
- **Acceptance Criteria**:
  - [ ] All screens implemented
  - [ ] Navigation working
  - [ ] Responsive design applied
- **Estimated Time**: 10-15 hours
- **Dependencies**: Task 3.1, Task 2.2
- **Assignee**: [Developer name]

### Phase 4: Integration & Testing
#### Task 4.1: API Integration
- **Description**: Connect frontend with backend APIs
- **Acceptance Criteria**:
  - [ ] API calls implemented
  - [ ] Error handling added
  - [ ] Loading states managed
- **Estimated Time**: 4-6 hours
- **Dependencies**: Task 3.2, Task 2.2
- **Assignee**: [Developer name]

#### Task 4.2: Testing
- **Description**: Implement comprehensive testing
- **Acceptance Criteria**:
  - [ ] Unit tests written
  - [ ] Integration tests added
  - [ ] E2E tests implemented
- **Estimated Time**: 6-10 hours
- **Dependencies**: Task 4.1
- **Assignee**: [Developer name]

### Phase 5: Deployment & Documentation
#### Task 5.1: Deployment Setup
- **Description**: Set up production deployment pipeline
- **Acceptance Criteria**:
  - [ ] CI/CD pipeline configured
  - [ ] Production environment set up
  - [ ] Monitoring implemented
- **Estimated Time**: 4-6 hours
- **Dependencies**: Task 4.2
- **Assignee**: [Developer name]

#### Task 5.2: Documentation
- **Description**: Create user and developer documentation
- **Acceptance Criteria**:
  - [ ] User manual written
  - [ ] API documentation generated
  - [ ] Developer guide created
- **Estimated Time**: 3-5 hours
- **Dependencies**: Task 5.1
- **Assignee**: [Developer name]

## Task Summary

### Total Estimated Time
- **Minimum**: 50 hours
- **Maximum**: 77 hours
- **Average**: 63.5 hours

### Critical Path
Task 1.1 → Task 1.2 → Task 2.1 → Task 2.2 → Task 3.2 → Task 4.1 → Task 4.2 → Task 5.1 → Task 5.2

### Parallel Tasks
- Task 3.1 can be done in parallel with Task 2.2
- Task 5.2 can be done in parallel with Task 5.1

## Risk Mitigation

### Technical Risks
- **Complex integration**: Add buffer time for integration tasks
- **Performance issues**: Include performance testing in Task 4.2
- **Security vulnerabilities**: Include security review in each phase

### Resource Risks
- **Developer availability**: Plan for knowledge transfer
- **Skill gaps**: Include learning time in estimates
- **External dependencies**: Identify and track external blockers

## Next Steps
1. Review and refine task breakdown
2. Assign tasks to team members
3. Set up project tracking system
4. Begin with Task 1.1: Environment Setup

---
*Generated at: ${getCurrentTimestamp()}*
*Please update this document according to actual project requirements*
`;

        await fs.writeFile(path.join(specDir, "tasks.md"), tasksTemplate, "utf-8");

        // Update specification status
        specConfig.status = 'tasks';
        specConfig.updatedAt = getCurrentTimestamp();
        config.specs[specName] = specConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Task breakdown generated successfully!\n\nSpecification: ${specConfig.title}\nStatus: ${specConfig.status}\n\nTask breakdown saved to: .vibecode/workflows/specs/${specName}/tasks.md\n\nNext step: Run \`spec-execute\` to start implementation.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to generate task breakdown: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // Execute specification tasks
  server.tool(
    "spec-execute",
    "Execute tasks for current specification",
    {
      rootPath: z.string().describe("Project root directory path"),
      specName: z.string().describe("Specification name"),
      taskNumber: z.number().optional().describe("Specific task number to execute"),
    },
    async ({ rootPath, specName, taskNumber }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (!config.specs[specName]) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Specification "${specName}" does not exist. Please create it first using spec-create.`,
              },
            ],
          };
        }

        const specConfig = config.specs[specName];
        const specDir = path.join(rootPath, ".vibecode", "workflows", "specs", specName);

        // Check if tasks document exists
        const tasksPath = path.join(specDir, "tasks.md");
        if (!existsSync(tasksPath)) {
          return {
            content: [
              {
                type: "text",
                text: `❌ Task breakdown not found. Please run \`spec-tasks\` first to generate task breakdown.`,
              },
            ],
          };
        }

        const tasksContent = await fs.readFile(tasksPath, "utf-8");

        // Create execution log
        const executionTemplate = `# ${specConfig.title} - Execution Log

## Current Status
- **Specification**: ${specConfig.title}
- **Status**: ${specConfig.status}
- **Started**: ${getCurrentTimestamp()}
- **Current Task**: ${taskNumber || 'Not specified'}

## Task Reference
${tasksContent}

## Execution Progress

### Completed Tasks
- [ ] Task 1.1: Environment Setup
- [ ] Task 1.2: Database Setup
- [ ] Task 2.1: API Foundation
- [ ] Task 2.2: Core Business Logic
- [ ] Task 3.1: UI Components
- [ ] Task 3.2: User Interface
- [ ] Task 4.1: API Integration
- [ ] Task 4.2: Testing
- [ ] Task 5.1: Deployment Setup
- [ ] Task 5.2: Documentation

### Current Task Details
${taskNumber ? `**Task ${taskNumber}**: [Task description from tasks.md]` : 'Please specify a task number to execute'}

### Implementation Notes
- [Add implementation notes here]
- [Document any issues or decisions]
- [Track progress and blockers]

### Code Changes
\`\`\`
[List files created/modified during implementation]
\`\`\`

### Testing Results
- [ ] Unit tests passing
- [ ] Integration tests passing
- [ ] Manual testing completed

### Next Steps
1. [Next action item]
2. [Next action item]
3. [Next action item]

---
*Execution started: ${getCurrentTimestamp()}*
*Last updated: ${getCurrentTimestamp()}*
`;

        await fs.writeFile(path.join(specDir, "execution.md"), executionTemplate, "utf-8");

        // Update specification status
        specConfig.status = 'implementing';
        specConfig.updatedAt = getCurrentTimestamp();
        if (taskNumber) {
          specConfig.currentTask = taskNumber;
        }
        config.specs[specName] = specConfig;
        await saveWorkflowConfig(rootPath, config);

        return {
          content: [
            {
              type: "text",
              text: `✅ Execution started for specification: ${specConfig.title}\n\nStatus: ${specConfig.status}\n${taskNumber ? `Current Task: ${taskNumber}\n` : ''}Execution log saved to: .vibecode/workflows/specs/${specName}/execution.md\n\n📋 **Implementation Guidelines:**\n1. Follow the task breakdown in tasks.md\n2. Update execution.md with progress\n3. Implement tests for each component\n4. Document any design decisions\n5. Update status when tasks are completed\n\n🎯 **Next Steps:**\n- Review task breakdown\n- Start with Task 1.1 if no specific task specified\n- Update execution log with progress\n- Run tests after each implementation`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to start execution: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // View specification status
  server.tool(
    "spec-status",
    "View current status of specifications",
    {
      rootPath: z.string().describe("Project root directory path"),
      specName: z.string().optional().describe("Specification name, if not provided shows all specifications"),
    },
    async ({ rootPath, specName }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (Object.keys(config.specs).length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications currently exist. Use `spec-create` to create new specifications.",
              },
            ],
          };
        }

        if (specName) {
          // Show specific specification status
          if (!config.specs[specName]) {
            return {
              content: [
                {
                  type: "text",
                  text: `❌ Specification "${specName}" does not exist.`,
                },
              ],
            };
          }

          const spec = config.specs[specName];
          const statusEmoji = {
            'created': '🆕',
            'requirements': '📋',
            'design': '🎨',
            'tasks': '📝',
            'implementing': '⚙️',
            'completed': '✅'
          };

          return {
            content: [
              {
                type: "text",
                text: `📊 Specification status: ${spec.title}\n\n${statusEmoji[spec.status]} Current status: ${spec.status}\n📅 Created at: ${spec.createdAt}\n🔄 Last updated: ${spec.updatedAt}${spec.currentTask ? `\n📌 Current task: ${spec.currentTask}` : ''}`,
              },
            ],
          };
        } else {
          // Show all specifications status
          const specList = Object.values(config.specs).map(spec => {
            const statusEmoji = {
              'created': '🆕',
              'requirements': '📋',
              'design': '🎨',
              'tasks': '📝',
              'implementing': '⚙️',
              'completed': '✅'
            };
            return `${statusEmoji[spec.status]} ${spec.title} (${spec.name}) - ${spec.status}`;
          }).join('\n');

          return {
            content: [
              {
                type: "text",
                text: `📊 All specifications status:\n\n${specList}`,
              },
            ],
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to get specification status: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // List all specifications
  server.tool(
    "spec-list",
    "List all specifications",
    {
      rootPath: z.string().describe("Project root directory path"),
    },
    async ({ rootPath }) => {
      try {
        const config = await loadWorkflowConfig(rootPath);

        if (Object.keys(config.specs).length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "📋 No specifications currently exist. Use `spec-create` to create new specifications.",
              },
            ],
          };
        }

        const specList = Object.values(config.specs).map((spec, index) => {
          const statusEmoji = {
            'created': '🆕',
            'requirements': '📋',
            'design': '🎨',
            'tasks': '📝',
            'implementing': '⚙️',
            'completed': '✅'
          };

          return `${index + 1}. ${statusEmoji[spec.status]} **${spec.title}**\n   - Name: ${spec.name}\n   - Status: ${spec.status}\n   - Created: ${spec.createdAt.split('T')[0]}\n   - Updated: ${spec.updatedAt.split('T')[0]}`;
        }).join('\n\n');

        return {
          content: [
            {
              type: "text",
              text: `📋 Specification list (${Object.keys(config.specs).length} total):\n\n${specList}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ Failed to get specification list: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}
