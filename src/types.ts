/**
 * Type definitions for Vibe Coding workflow system
 */

export interface SpecConfig {
  name: string;
  title: string;
  status: 'created' | 'requirements' | 'design' | 'tasks' | 'implementing' | 'completed';
  createdAt: string;
  updatedAt: string;
  currentTask?: number;
}

export interface BugConfig {
  name: string;
  title: string;
  status: 'reported' | 'analyzing' | 'fixing' | 'verifying' | 'resolved';
  createdAt: string;
  updatedAt: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SubAgentsConfig {
  enabled: boolean;
  version: string;
  qualityGates: {
    defaultThreshold: number;
    phaseThresholds: Record<string, number>;
    overallThreshold: number;
    strictMode: boolean;
  };
  agents: {
    orchestrator: {
      enabled: boolean;
      maxRetries: number;
      maxWorkflowRetries: number;
      timeout: number;
    };
    spec: {
      enabled: boolean;
      timeout: number;
      outputFormat: string;
    };
    architect: {
      enabled: boolean;
      timeout: number;
      preferredStack: string;
    };
    developer: {
      enabled: boolean;
      timeout: number;
      codeQuality: string;
      improvementBonus: number;
    };
    quality: {
      enabled: boolean;
      timeout: number;
      securityScan: boolean;
      performanceCheck: boolean;
    };
    test: {
      enabled: boolean;
      timeout: number;
      coverageTarget: number;
      testTypes: string[];
    };
  };
  workflow: {
    phases: string[];
    autoRestart: boolean;
    continueOnPhaseFailure: boolean;
    progressReporting: boolean;
  };
  output: {
    format: string;
    showProgress: boolean;
    showQualityScores: boolean;
    saveResults: boolean;
    resultPath: string;
  };
  communication: {
    messageBus: {
      timeout: number;
      retryAttempts: number;
      bufferSize: number;
    };
    logging: {
      level: string;
      saveToFile: boolean;
      logPath: string;
    };
  };
}

export interface WorkflowConfig {
  specs: Record<string, SpecConfig>;
  bugs: Record<string, BugConfig>;
  version: string;
  subAgents?: SubAgentsConfig;
}

export type SeverityLevel = 'low' | 'medium' | 'high' | 'critical';
export type SpecStatus = 'created' | 'requirements' | 'design' | 'tasks' | 'implementing' | 'completed';
export type BugStatus = 'reported' | 'analyzing' | 'fixing' | 'verifying' | 'resolved';
export type SteeringType = 'all' | 'product' | 'tech' | 'structure';
