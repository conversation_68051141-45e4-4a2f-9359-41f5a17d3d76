/**
 * 简化的重启功能测试
 * v1.4.0 - Sub-Agents 革命
 */

import { MessageBus } from "./src/agents/communication/message-bus.js";
import { AgentFactory } from "./src/agents/base-agent.js";
import { 
  AgentContext, 
  QualityGate, 
  WorkflowResult,
  AgentType 
} from "./src/agents/types.js";

/**
 * 测试从头重启功能
 */
async function testRestartSimple() {
  console.log('🧪 Testing Workflow Restart Functionality (Simple)...\n');

  try {
    // 创建工作流程上下文（设置中等质量阈值）
    const workflowId = `test-restart-${Date.now()}`;
    const threshold = 90; // 设置90%阈值，可能触发重启
    
    const context: AgentContext = {
      projectRoot: './test-project',
      workflowId,
      currentPhase: 'initialization',
      sharedState: {},
      qualityGates: createQualityGates(threshold)
    };

    console.log(`📋 Test Configuration:`);
    console.log(`   Workflow ID: ${workflowId}`);
    console.log(`   Quality Threshold: ${threshold}%`);
    console.log(`   Max Workflow Retries: 2`);
    console.log(`   Expected: May trigger restart if quality < ${threshold}%\n`);

    // 创建消息总线
    const messageBus = new MessageBus();

    // 创建并启动代理
    await createAndStartAgents(context, messageBus);
    console.log('✅ All agents started successfully\n');

    // 启动工作流程测试
    console.log('🚀 Starting workflow test...\n');
    
    const result = await executeWorkflowTest(messageBus, {
      title: '用户认证系统',
      description: '开发用户认证系统',
      qualityThreshold: threshold
    });

    // 显示测试结果
    displayTestResults(result, threshold);

    // 清理资源
    await messageBus.shutdown();
    console.log('🧹 Test cleanup completed\n');

    console.log('🎉 Restart Test Completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

/**
 * 创建质量门控（较宽松的阈值）
 */
function createQualityGates(threshold: number): QualityGate[] {
  return [
    {
      name: "Requirements Quality",
      phase: "requirements",
      threshold: Math.max(80, threshold - 10),
      metric: "completeness",
      required: true
    },
    {
      name: "Architecture Quality", 
      phase: "architecture",
      threshold: Math.max(80, threshold - 10),
      metric: "design_quality",
      required: true
    },
    {
      name: "Code Quality",
      phase: "implementation", 
      threshold: Math.max(80, threshold - 10),
      metric: "code_quality",
      required: true
    },
    {
      name: "Security Quality",
      phase: "quality",
      threshold: Math.max(70, threshold - 15),
      metric: "security_score",
      required: true
    },
    {
      name: "Test Coverage",
      phase: "testing",
      threshold: Math.max(70, threshold - 15),
      metric: "test_coverage",
      required: true
    },
    {
      name: "Integration Quality",
      phase: "integration",
      threshold: Math.max(70, threshold - 15),
      metric: "integration_quality",
      required: true
    }
  ];
}

/**
 * 创建并启动代理
 */
async function createAndStartAgents(
  context: AgentContext, 
  messageBus: MessageBus
): Promise<void> {
  const agentsToCreate: AgentType[] = ["spec", "architect", "developer", "quality", "test"];

  // 总是创建 Orchestrator
  const orchestrator = await AgentFactory.createAgent("orchestrator", context);
  messageBus.registerAgent(orchestrator);
  await orchestrator.start();

  // 创建其他代理
  for (const agentType of agentsToCreate) {
    try {
      const agent = await AgentFactory.createAgent(agentType, context);
      messageBus.registerAgent(agent);
      await agent.start();
    } catch (error) {
      console.error(`❌ Failed to create ${agentType} agent:`, error);
    }
  }
}

/**
 * 执行工作流程测试
 */
async function executeWorkflowTest(messageBus: MessageBus, workflowData: any): Promise<WorkflowResult> {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Workflow test timeout (60 seconds)'));
    }, 60000);

    let phaseCount = 0;
    let workflowAttempt = 1;

    // 监听工作流程进度
    messageBus.on('message', (message) => {
      if (message.type === 'response' && message.payload.phase) {
        phaseCount++;
        const phase = message.payload.phase;
        const success = message.payload.success;
        const qualityScore = message.payload.qualityScore || 0;
        
        console.log(`   ${success ? '✅' : '❌'} Attempt ${workflowAttempt} - Phase ${phaseCount}: ${phase} (${qualityScore}%)`);
      }

      // 检测工作流程重启
      if (message.payload && message.payload.message && 
          message.payload.message.includes('Restarting entire workflow')) {
        workflowAttempt++;
        phaseCount = 0;
        console.log(`\n🔄 WORKFLOW RESTART DETECTED - Starting Attempt ${workflowAttempt}\n`);
      }

      // 工作流程成功完成
      if (message.type === 'notification' && message.payload.message === 'Workflow completed successfully') {
        clearTimeout(timeout);
        console.log(`\n✅ Workflow completed successfully on attempt ${workflowAttempt}!`);
        resolve(message.payload.result);
      }

      // 工作流程最终失败
      if (message.type === 'notification' && message.payload.message === 'Workflow failed after maximum retries') {
        clearTimeout(timeout);
        console.log(`\n❌ Workflow failed after maximum retries`);
        resolve(message.payload.result);
      }

      // 处理其他失败情况
      if (message.type === 'notification' && message.payload.message === 'Workflow aborted') {
        clearTimeout(timeout);
        console.log('\n❌ Workflow was aborted');
        
        const failedResult: WorkflowResult = {
          success: false,
          workflowId: workflowData.workflowId || 'test-workflow',
          duration: Date.now(),
          phases: [],
          finalState: {},
          qualityReport: {
            overallScore: 0,
            phaseScores: {},
            gatesPassed: 0,
            gatesTotal: 6,
            recommendations: ['Workflow aborted']
          }
        };
        resolve(failedResult);
      }
    });

    // 启动工作流程
    messageBus.sendMessage({
      id: `test-start-${Date.now()}`,
      type: 'request',
      from: 'test-user',
      to: 'orchestrator',
      payload: {
        action: 'start-workflow',
        data: workflowData
      }
    }).catch(reject);
  });
}

/**
 * 显示测试结果
 */
function displayTestResults(result: WorkflowResult, threshold: number): void {
  console.log('📊 Restart Test Results:');
  console.log('=' .repeat(50));
  
  console.log(`🎯 Workflow ID: ${result.workflowId}`);
  console.log(`⏱️  Duration: ${Math.round(result.duration / 1000)}s`);
  console.log(`✅ Success: ${result.success ? 'Yes' : 'No'}`);
  console.log(`🎯 Quality Threshold: ${threshold}%`);
  console.log('');

  console.log('📈 Quality Report:');
  console.log(`   Overall Score: ${result.qualityReport.overallScore}%`);
  console.log(`   Quality Gates: ${result.qualityReport.gatesPassed}/${result.qualityReport.gatesTotal} passed`);
  console.log(`   Threshold Met: ${result.qualityReport.overallScore >= threshold ? '✅ Yes' : '❌ No'}`);
  console.log('');

  if (result.phases.length > 0) {
    console.log('🔄 Phase Results:');
    result.phases.forEach((phase, index) => {
      const status = phase.success ? '✅' : '❌';
      const duration = Math.round(phase.duration / 1000);
      console.log(`   ${status} ${index + 1}. ${phase.phase} (${phase.qualityScore}%) - ${duration}s`);
    });
    console.log('');
  }

  console.log('🧪 Test Validation:');
  if (result.success) {
    console.log('   ✅ Workflow completed successfully');
    if (result.qualityReport.overallScore >= threshold) {
      console.log('   🏆 Quality threshold met!');
    } else {
      console.log('   ⚠️  Quality threshold not met but workflow completed');
    }
  } else {
    console.log('   ❌ Workflow failed');
    if (result.qualityReport.recommendations.some(r => r.includes('maximum retries'))) {
      console.log('   ✅ Failed due to maximum retries - restart mechanism working');
    }
  }
  
  console.log('');
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testRestartSimple().catch(console.error);
}

export { testRestartSimple };
