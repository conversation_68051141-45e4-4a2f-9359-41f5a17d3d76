/**
 * Vibe Coding Sub-Agents 模拟测试
 * v1.4.0 - Sub-Agents 革命
 */

import { MessageBus } from "./src/agents/communication/message-bus.js";
import { AgentFactory } from "./src/agents/base-agent.js";
import { 
  AgentContext, 
  QualityGate, 
  WorkflowResult,
  AgentType 
} from "./src/agents/types.js";

/**
 * 模拟测试 vibe-coding 工具
 */
async function testVibeCoding() {
  console.log('🚀 Starting Vibe Coding Sub-Agents Test...\n');

  try {
    // 创建工作流程上下文
    const workflowId = `test-workflow-${Date.now()}`;
    const context: AgentContext = {
      projectRoot: './test-project',
      workflowId,
      currentPhase: 'initialization',
      sharedState: {},
      qualityGates: createQualityGates(95)
    };

    console.log(`📋 Test Configuration:`);
    console.log(`   Workflow ID: ${workflowId}`);
    console.log(`   Project Root: ${context.projectRoot}`);
    console.log(`   Quality Threshold: 95%`);
    console.log(`   Quality Gates: ${context.qualityGates.length}\n`);

    // 创建消息总线
    const messageBus = new MessageBus();
    console.log('📡 Message Bus created\n');

    // 创建并启动代理
    console.log('🤖 Creating and starting agents...');
    await createAndStartAgents(context, messageBus);
    console.log('✅ All agents started successfully\n');

    // 显示代理状态
    const agentsStatus = messageBus.getAgentsStatus();
    console.log('📊 Agents Status:');
    agentsStatus.forEach(agent => {
      console.log(`   ${agent.displayName}: ${agent.isActive ? '🟢 Active' : '🔴 Inactive'}`);
    });
    console.log('');

    // 启动工作流程测试
    console.log('🚀 Starting workflow test: "开发用户认证系统"\n');
    
    const result = await executeWorkflowTest(messageBus, {
      title: '用户认证系统',
      description: '开发用户认证系统',
      qualityThreshold: 95
    });

    // 显示测试结果
    displayTestResults(result);

    // 清理资源
    await messageBus.shutdown();
    console.log('🧹 Test cleanup completed\n');

    console.log('🎉 Vibe Coding Sub-Agents Test Completed Successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

/**
 * 创建质量门控
 */
function createQualityGates(threshold: number): QualityGate[] {
  return [
    {
      name: "Requirements Quality",
      phase: "requirements",
      threshold,
      metric: "completeness",
      required: true
    },
    {
      name: "Architecture Quality", 
      phase: "architecture",
      threshold,
      metric: "design_quality",
      required: true
    },
    {
      name: "Code Quality",
      phase: "implementation", 
      threshold,
      metric: "code_quality",
      required: true
    },
    {
      name: "Security Quality",
      phase: "quality",
      threshold,
      metric: "security_score",
      required: true
    },
    {
      name: "Test Coverage",
      phase: "testing",
      threshold: Math.max(80, threshold - 10),
      metric: "test_coverage",
      required: true
    }
  ];
}

/**
 * 创建并启动代理
 */
async function createAndStartAgents(
  context: AgentContext, 
  messageBus: MessageBus
): Promise<void> {
  const agentsToCreate: AgentType[] = ["spec", "architect", "developer", "quality", "test"];

  // 总是创建 Orchestrator
  const orchestrator = await AgentFactory.createAgent("orchestrator", context);
  messageBus.registerAgent(orchestrator);
  await orchestrator.start();

  // 创建其他代理
  for (const agentType of agentsToCreate) {
    try {
      const agent = await AgentFactory.createAgent(agentType, context);
      messageBus.registerAgent(agent);
      await agent.start();
    } catch (error) {
      console.error(`❌ Failed to create ${agentType} agent:`, error);
    }
  }
}

/**
 * 执行工作流程测试
 */
async function executeWorkflowTest(messageBus: MessageBus, workflowData: any): Promise<WorkflowResult> {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('Workflow test timeout (60 seconds)'));
    }, 60000); // 60秒超时

    let phaseCount = 0;
    const expectedPhases = 8; // initialization, requirements, architecture, implementation, quality, testing, integration, completion

    // 监听工作流程进度
    messageBus.on('message', (message) => {
      if (message.type === 'response' && message.payload.phase) {
        phaseCount++;
        const phase = message.payload.phase;
        const success = message.payload.success;
        const qualityScore = message.payload.qualityScore || 0;
        
        console.log(`   ${success ? '✅' : '❌'} Phase ${phaseCount}/${expectedPhases}: ${phase} (${qualityScore}%)`);
      }

      if (message.type === 'notification' && message.payload.message === 'Workflow completed successfully') {
        clearTimeout(timeout);
        console.log('');
        resolve(message.payload.result);
      }
    });

    // 启动工作流程
    messageBus.sendMessage({
      id: `test-start-${Date.now()}`,
      type: 'request',
      from: 'test-user',
      to: 'orchestrator',
      payload: {
        action: 'start-workflow',
        data: workflowData
      }
    }).catch(reject);
  });
}

/**
 * 显示测试结果
 */
function displayTestResults(result: WorkflowResult): void {
  console.log('📊 Test Results Summary:');
  console.log('=' .repeat(50));
  
  console.log(`🎯 Workflow ID: ${result.workflowId}`);
  console.log(`⏱️  Duration: ${Math.round(result.duration / 1000)}s`);
  console.log(`✅ Success: ${result.success ? 'Yes' : 'No'}`);
  console.log('');

  console.log('📈 Quality Report:');
  console.log(`   Overall Score: ${result.qualityReport.overallScore}%`);
  console.log(`   Quality Gates: ${result.qualityReport.gatesPassed}/${result.qualityReport.gatesTotal} passed`);
  console.log('');

  console.log('🔄 Phase Results:');
  result.phases.forEach((phase, index) => {
    const status = phase.success ? '✅' : '❌';
    const duration = Math.round(phase.duration / 1000);
    console.log(`   ${status} ${index + 1}. ${phase.phase} (${phase.qualityScore}%) - ${duration}s`);
  });
  console.log('');

  console.log('📋 Generated Artifacts:');
  if (result.finalState.requirements) {
    console.log('   ✅ Requirements Specification');
    console.log(`      - User Stories: ${result.finalState.requirements.userStories?.length || 0}`);
    console.log(`      - Acceptance Criteria: ${result.finalState.requirements.acceptanceCriteria?.length || 0}`);
  }
  
  if (result.finalState.architecture) {
    console.log('   ✅ System Architecture Design');
    console.log(`      - API Endpoints: ${result.finalState.architecture.apiDesign?.endpoints?.length || 0}`);
    console.log(`      - Data Entities: ${result.finalState.architecture.dataModel?.entities?.length || 0}`);
  }
  
  if (result.finalState.implementation) {
    console.log('   ✅ Implementation Code');
    console.log(`      - Generated Files: ${result.finalState.implementation.generatedFiles?.length || 0}`);
    console.log(`      - Code Quality: ${result.finalState.implementation.codeQuality?.overallGrade || 'N/A'}`);
  }
  
  if (result.finalState.quality) {
    console.log('   ✅ Quality Analysis Report');
    console.log(`      - Security Score: ${result.finalState.quality.securityScan?.riskScore || 0}%`);
    console.log(`      - Performance Score: ${result.finalState.quality.performanceAssessment?.score || 0}%`);
  }
  
  if (result.finalState.testing) {
    console.log('   ✅ Test Suite');
    console.log(`      - Test Files: ${result.finalState.testing.testFiles?.length || 0}`);
    console.log(`      - Coverage: ${result.finalState.testing.coverageReport?.overall || 0}%`);
  }
  
  console.log('');
  console.log('🎯 Test Conclusion:');
  if (result.qualityReport.overallScore >= 90) {
    console.log('   🏆 EXCELLENT - All quality standards exceeded!');
  } else if (result.qualityReport.overallScore >= 80) {
    console.log('   ✅ GOOD - Quality standards met successfully!');
  } else {
    console.log('   ⚠️  NEEDS IMPROVEMENT - Some quality issues detected');
  }
  console.log('');
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testVibeCoding().catch(console.error);
}

export { testVibeCoding };
